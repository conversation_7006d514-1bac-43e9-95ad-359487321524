#include "utils/ExcelExporter.h"
#include "modules/detection/YoloAlgorithm.h" // 包含Detection定义
#include "utils/Logger.h"
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QDir>
#include <QStringConverter>

namespace utils {
namespace ExcelExporter {

bool exportDetections(const std::vector<std::tuple<std::string, Detection>>& results, const std::string& filepath) {
    try {
        // 创建CSV文件
        QFile file(QString::fromStdString(filepath));
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            utils::Logger::instance().error(QString("无法创建文件: %1").arg(QString::fromStdString(filepath)));
            return false;
        }

        QTextStream out(&file);
        out.setEncoding(QStringConverter::Utf8);
        out.setGenerateByteOrderMark(true);

        // 写入表头
        out << "filename,label,confidence,X,Y,width,height\n";

        // 写入数据
        for (const auto& result : results) {
            const std::string& filename = std::get<0>(result);
            const Detection& detection = std::get<1>(result);

            out << QString::fromStdString(filename) << ","
                << QString::fromStdString(detection.label) << ","
                << QString::number(detection.confidence, 'f', 4) << ","
                << QString::number(detection.bbox.x) << ","
                << QString::number(detection.bbox.y) << ","
                << QString::number(detection.bbox.width) << ","
                << QString::number(detection.bbox.height) << "\n";
        }

        file.close();
        utils::Logger::instance().debug(QString("检测结果已导出到: %1").arg(QString::fromStdString(filepath)));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::instance().error(QString("导出检测结果时出错: %1").arg(e.what()));
        return false;
    }
}

bool exportCountsWithAllFiles(const std::vector<std::tuple<std::string, Detection>>& results,
                              const std::vector<std::string>& allFiles,
                              const std::string& filepath) {
    try {
        // 创建CSV文件
        QFile file(QString::fromStdString(filepath));
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            utils::Logger::instance().error(QString("无法创建文件: %1").arg(QString::fromStdString(filepath)));
            return false;
        }

        QTextStream out(&file);
        out.setEncoding(QStringConverter::Utf8);
        out.setGenerateByteOrderMark(true);

        // 收集所有类别
        std::set<std::string> allLabels;
        std::map<std::string, std::map<std::string, int>> fileLabelCounts; // filename -> {label -> count}

        // 统计每张图片每个类别的检测数量
        for (const auto& result : results) {
            const std::string& filename = std::get<0>(result);
            const Detection& detection = std::get<1>(result);
            const std::string& label = detection.label;

            allLabels.insert(label);
            fileLabelCounts[filename][label]++;
        }

        // 写入表头：filename + 所有类别名称
        out << "filename";
        for (const auto& label : allLabels) {
            out << "," << QString::fromStdString(label);
        }
        out << "\n";

        // 写入数据：每个文件一行，包含每个类别的计数（没有检测到的类别显示0）
        for (const auto& filename : allFiles) {
            out << QString::fromStdString(filename);
            for (const auto& label : allLabels) {
                int count = 0;
                if (fileLabelCounts[filename].find(label) != fileLabelCounts[filename].end()) {
                    count = fileLabelCounts[filename][label];
                }
                out << "," << count;
            }
            out << "\n";
        }

        file.close();
        utils::Logger::instance().debug(QString("计数结果已导出到: %1").arg(QString::fromStdString(filepath)));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::instance().error(QString("导出计数结果时出错: %1").arg(e.what()));
        return false;
    }
}

} // namespace ExcelExporter
} // namespace utils