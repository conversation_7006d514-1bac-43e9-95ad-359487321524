#include "utils/Logger.h"

namespace utils {

Logger& utils::Logger::instance() {
    static Logger instance;
    return instance;
}

Logger::Logger()
    : currentLevel_(Level::Info)
    , fileLoggingEnabled_(false) {
}

Logger::~Logger() {
    if (logStream_) {
        logStream_->flush();
    }
}

void Logger::debug(const QString& message) {
    log(Level::Debug, message);
}

void Logger::warning(const QString& message) {
    log(Level::Warning, message);
}

void Logger::error(const QString& message) {
    log(Level::Error, message);
}

void Logger::info(const QString& message) {
    log(Level::Info, message);
}

void Logger::registerCallback(LogCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    callbacks_.push_back(callback);
}

void Logger::clearCallback(LogCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    callbacks_.clear();
}

bool Logger::setLogFile(const QString& filePath) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto newFile = std::make_unique<QFile>(filePath);
    if (!newFile->open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        return false;
    }

    logFile_ = std::move(newFile);
    logStream_ = std::make_unique<QTextStream>(logFile_.get());
    return true;
}

void Logger::enableFileLogging(bool enable) {
    fileLoggingEnabled_ = enable;
}

void Logger::setLogLevel(Level level) {
    currentLevel_ = level;
}

void Logger::log(Level level, const QString& message) {
    if (level < currentLevel_) {
        return;
    }

    QString formattedMessage = QString("[%1] %2 - %3")
        .arg(levelToString(level))
        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
        .arg(message);

    std::lock_guard<std::mutex> lock(mutex_);

    // 通知回调
    for (const auto& callback : callbacks_) {
        callback(level, formattedMessage);
    }

    // 写入文件
    if (fileLoggingEnabled_ && logStream_) {
        *logStream_ << formattedMessage << Qt::endl;
    }
}

QString Logger::levelToString(Level level) const {
    switch (level) {
        case Level::Debug:   return "Debug";
        case Level::Warning: return "Warning";
        case Level::Error:   return "Error";
        case Level::Info:    return "Info";
        default:            return "Unknown";
    }
}

} // namespace utils