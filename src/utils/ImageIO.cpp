#include "utils/ImageIO.h"
#include "utils/Logger.h"
#include <QString>
#include <opencv2/imgcodecs.hpp>
#include <QFile>         // 用于文件操作
#include <QFileInfo>     // 用于获取文件后缀名
#include <QByteArray>    // 用于数据缓冲区
#include <QString>       // 用于文件路径字符串
#include <vector>        // 用于OpenCV编码缓冲区
#include <string>        // 用于后缀名转换

#include <opencv2/opencv.hpp> // 包含核心和图像编解码头文件

namespace utils {
namespace ImageIO {

cv::Mat imageRead(const QString& imagePath) {
    // 检查是否为RAW格式
    if (isRawFormat(imagePath)) {
        // 智能检测RAW图像尺寸
        auto size = guessRawImageSize(imagePath, 1);  // 假设单通道8位数据
        return readRawImage(imagePath, size.first, size.second, 1, CV_8U);
    }

    // 读取文件内容到内存
    QFile file(imagePath);
    if (!file.open(QIODevice::ReadOnly)) {
        utils::Logger::instance().warning(QString("无法打开文件: %1").arg(imagePath));
        return cv::Mat();
    }
    QByteArray fileData = file.readAll();
    file.close();

    // 将文件内容转换为字节向量
    std::vector<uchar> buffer(fileData.begin(), fileData.end());

    // 使用 imdecode 读取图像
    cv::Mat img = cv::imdecode(buffer, cv::IMREAD_COLOR);
    if (img.empty()) {
        utils::Logger::instance().warning(QString("无法解码图像: %1").arg(imagePath));
    }
    return img;
}

bool imageWrite(const QString& imagePath, const cv::Mat& img) {
    // 检查输入图像是否有效
    if (img.empty()) {
        utils::Logger::instance().warning(QString("无法写入空图像到: %1").arg(imagePath));
        return false;
    }

    // 获取文件格式（如jpg, png等）
    QFileInfo fileInfo(imagePath);
    QString suffix = fileInfo.suffix().toLower();
    if (suffix.isEmpty()) {
        utils::Logger::instance().warning(QString("无法确定图像格式: %1").arg(imagePath));
        return false;
    }

    try {
        // 将图像编码到内存缓冲区
        std::vector<uchar> buffer;
        cv::imencode("." + suffix.toStdString(), img, buffer);

        // 将编码后的数据写入文件
        QFile file(imagePath);
        if (!file.open(QIODevice::WriteOnly)) {
            utils::Logger::instance().warning(QString("无法打开文件: %1").arg(imagePath));
            return false;
        }

        // 写入数据并验证是否完整写入
        qint64 bytesWritten = file.write(reinterpret_cast<const char*>(buffer.data()), buffer.size());
        file.close();

        return bytesWritten == static_cast<qint64>(buffer.size());
    } catch (const cv::Exception& ex) {
        utils::Logger::instance().warning(QString("写入图像时出错: %1").arg(imagePath));
        return false;
    }
}

cv::Mat readRawImage(const QString& imagePath, int width, int height, int channels, int depth) {
    QFile file(imagePath);
    if (!file.open(QIODevice::ReadOnly)) {
        utils::Logger::instance().warning(QString("无法打开RAW文件: %1").arg(imagePath));
        return cv::Mat();
    }

    // 对于RAW格式，通常是单通道Bayer数据
    // 计算期望的数据大小（单通道）
    int bytesPerPixel = (depth == CV_8U ? 1 : (depth == CV_16U ? 2 : 4));
    qint64 expectedSize = static_cast<qint64>(width) * height * bytesPerPixel;

    // 检查文件大小
    qint64 fileSize = file.size();
    if (fileSize < expectedSize) {
        utils::Logger::instance().warning(QString("RAW文件大小不匹配: 期望%1字节，实际%2字节").arg(expectedSize).arg(fileSize));
        file.close();
        return cv::Mat();
    }

    // 读取数据
    QByteArray rawData = file.read(expectedSize);
    file.close();

    if (rawData.size() != expectedSize) {
        utils::Logger::instance().warning(QString("RAW数据读取不完整: 期望%1字节，读取%2字节").arg(expectedSize).arg(rawData.size()));
        return cv::Mat();
    }

    // 创建单通道OpenCV Mat
    int cvType = CV_MAKETYPE(depth, 1);  // 强制单通道
    cv::Mat rawImg(height, width, cvType, (void*)rawData.data());

    // 复制数据以避免内存问题
    cv::Mat bayerImg = rawImg.clone();

    // 进行Bayer去马赛克处理，转换为BGR彩色图像
    cv::Mat bgrImg;

    // 尝试不同的Bayer模式
    std::vector<int> bayerModes = {
        cv::COLOR_BayerBG2BGR,  // BG模式（最常见）
        cv::COLOR_BayerGB2BGR,  // GB模式
        cv::COLOR_BayerRG2BGR,  // RG模式
        cv::COLOR_BayerGR2BGR   // GR模式
    };

    std::vector<QString> bayerNames = {"BayerBG2BGR", "BayerGB2BGR", "BayerRG2BGR", "BayerGR2BGR"};

    bool success = false;
    for (size_t i = 0; i < bayerModes.size(); ++i) {
        try {
            cv::cvtColor(bayerImg, bgrImg, bayerModes[i]);
            utils::Logger::instance().info(QString("RAW图像去马赛克成功，使用%1模式").arg(bayerNames[i]));
            success = true;
            break;
        } catch (const cv::Exception& e) {
            // 继续尝试下一个模式
            continue;
        }
    }

    if (!success) {
        // 如果所有Bayer转换都失败，使用简单的灰度转BGR
        utils::Logger::instance().warning("所有Bayer去马赛克模式都失败，使用灰度模式");
        cv::cvtColor(bayerImg, bgrImg, cv::COLOR_GRAY2BGR);
    }

    return bgrImg;
}

cv::Mat readRawImageWithBayer(const QString& imagePath, int width, int height, int bayerMode, int depth) {
    QFile file(imagePath);
    if (!file.open(QIODevice::ReadOnly)) {
        utils::Logger::instance().warning(QString("无法打开RAW文件: %1").arg(imagePath));
        return cv::Mat();
    }

    // 计算期望的数据大小（单通道）
    int bytesPerPixel = (depth == CV_8U ? 1 : (depth == CV_16U ? 2 : 4));
    qint64 expectedSize = static_cast<qint64>(width) * height * bytesPerPixel;

    // 检查文件大小
    qint64 fileSize = file.size();
    if (fileSize < expectedSize) {
        utils::Logger::instance().warning(QString("RAW文件大小不匹配: 期望%1字节，实际%2字节").arg(expectedSize).arg(fileSize));
        file.close();
        return cv::Mat();
    }

    // 读取数据
    QByteArray rawData = file.read(expectedSize);
    file.close();

    if (rawData.size() != expectedSize) {
        utils::Logger::instance().warning(QString("RAW数据读取不完整: 期望%1字节，读取%2字节").arg(expectedSize).arg(rawData.size()));
        return cv::Mat();
    }

    // 创建单通道OpenCV Mat
    int cvType = CV_MAKETYPE(depth, 1);
    cv::Mat rawImg(height, width, cvType, (void*)rawData.data());

    // 复制数据以避免内存问题
    cv::Mat bayerImg = rawImg.clone();

    // 使用指定的Bayer模式进行去马赛克处理
    cv::Mat bgrImg;
    try {
        cv::cvtColor(bayerImg, bgrImg, bayerMode);
        utils::Logger::instance().info(QString("RAW图像去马赛克成功，使用指定Bayer模式: %1").arg(bayerMode));
    } catch (const cv::Exception& e) {
        // 如果指定的Bayer转换失败，使用灰度转BGR
        utils::Logger::instance().warning(QString("指定Bayer模式失败，使用灰度模式: %1").arg(e.what()));
        cv::cvtColor(bayerImg, bgrImg, cv::COLOR_GRAY2BGR);
    }

    return bgrImg;
}

bool isRawFormat(const QString& imagePath) {
    QFileInfo fileInfo(imagePath);
    QString suffix = fileInfo.suffix().toLower();

    // 常见的RAW格式扩展名
    QStringList rawExtensions = {"raw", "bin", "data", "yuv", "rgb", "bgr", "gray", "grey"};

    return rawExtensions.contains(suffix);
}

std::pair<int, int> guessRawImageSize(const QString& imagePath, int bytesPerPixel) {
    QFileInfo fileInfo(imagePath);
    qint64 fileSize = fileInfo.size();

    // 常见的图像尺寸组合
    std::vector<std::pair<int, int>> commonSizes = {
        {3088, 2064},  // 参考Python代码中的尺寸
        {1920, 1080},  // Full HD
        {1280, 720},   // HD
        {640, 480},    // VGA
        {800, 600},    // SVGA
        {1024, 768},   // XGA
        {1600, 1200},  // UXGA
        {2048, 1536},  // QXGA
        {4096, 3072},  // 4K
        {512, 512},    // 正方形
        {1024, 1024},  // 正方形
        {2048, 2048}   // 正方形
    };

    // 查找最匹配的尺寸
    for (const auto& size : commonSizes) {
        qint64 expectedSize = static_cast<qint64>(size.first) * size.second * bytesPerPixel;
        if (expectedSize == fileSize) {
            utils::Logger::instance().info(QString("检测到RAW图像尺寸: %1x%2").arg(size.first).arg(size.second));
            return size;
        }
    }

    // 如果没有找到匹配的尺寸，尝试计算可能的尺寸
    qint64 totalPixels = fileSize / bytesPerPixel;

    // 尝试找到接近正方形的尺寸
    int width = static_cast<int>(std::sqrt(totalPixels));
    int height = totalPixels / width;

    if (width * height * bytesPerPixel == fileSize) {
        utils::Logger::instance().info(QString("计算得到RAW图像尺寸: %1x%2").arg(width).arg(height));
        return {width, height};
    }

    // 默认返回参考尺寸
    utils::Logger::instance().warning(QString("无法确定RAW图像尺寸，使用默认值: 3088x2064"));
    return {3088, 2064};
}

} // namespace ImageIO
} // namespace utils
