#ifndef IMAGE_IO_H
#define IMAGE_IO_H

#include <opencv2/opencv.hpp>
#include <string>
#include <vector>
#include <QString>

// 前向声明Detection结构体
struct Detection;

namespace utils {
namespace ImageIO {
    // 图像读取函数
    cv::Mat imageRead(const QString& imagePath);
    // 图像写入函数
    bool imageWrite(const QString& imagePath, const cv::Mat& img);

    // RAW数据读取函数
    cv::Mat readRawImage(const QString& imagePath, int width, int height, int channels = 3, int depth = CV_8U);

    // 带Bayer模式的RAW数据读取函数
    cv::Mat readRawImageWithBayer(const QString& imagePath, int width, int height,
                                  int bayerMode = cv::COLOR_BayerBG2BGR, int depth = CV_8U);

    // 检查是否为RAW格式
    bool isRawFormat(const QString& imagePath);

    // 根据文件大小猜测RAW图像尺寸
    std::pair<int, int> guessRawImageSize(const QString& imagePath, int bytesPerPixel = 1);
} // namespace ImageIO
} // namespace utils

#endif // IMAGE_IO_H
