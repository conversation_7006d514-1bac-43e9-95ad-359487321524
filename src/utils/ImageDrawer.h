#ifndef IMAGE_DRAWER_H
#define IMAGE_DRAWER_H

#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

// 前向声明Detection结构体
struct Detection;

namespace utils {
namespace ImageDrawer {
    // 使用单一颜色参数的边界框绘制函数
    void drawBoundingBox(cv::Mat& image, const cv::Rect& box, const cv::Scalar& color, int thickness = 2);

    // 为了向后兼容保留的RGB参数版本
    void drawBoundingBox(cv::Mat& image, const cv::Rect& box, int r, int g, int b, int thickness = 2);

    // 普通文本绘制
    void drawText(cv::Mat& image, const std::string& text, const cv::Point& position, const cv::Scalar& color, float fontScale = 0.5, int thickness = 1);

    // 为了向后兼容保留的RGB参数版本
    void drawText(cv::Mat& image, const std::string& text, const cv::Point& position, int r, int g, int b, float fontScale = 0.5, int thickness = 1);

    // 带背景的文本绘制
    void drawTextWithBackground(cv::Mat& image, const std::string& text, const cv::Point& position,
                               const cv::Scalar& textColor, const cv::Scalar& bgColor,
                               float fontScale = 0.5, int thickness = 1, bool belowRect = false);
} // namespace ImageDrawer
} // namespace utils

#endif // IMAGE_DRAWER_H