#include "utils/ConfigManager.h"
#include <QJsonArray>
#include <QDateTime>
#include <QDir>

namespace utils {

ConfigManager& ConfigManager::instance() {
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::initialize(const QString& configPath) {
    QFile file(configPath);
    
    if (!file.open(QIODevice::ReadOnly)) {
        utils::Logger::instance().error("无法打开配置文件: " + configPath);
        return false;
    }

    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    
    if (doc.isNull()) {
        utils::Logger::instance().error("配置文件格式错误: " + configPath);
        return false;
    }

    config_ = doc.object();

    // 生成带时间戳的路径
    generateNewTimestampPaths();

    utils::Logger::instance().info("配置文件加载成功: " + configPath);
    utils::Logger::instance().info("当前保存路径: " + dynamicBaseDir_);
    return true;
}

QString ConfigManager::getWBCModelPath() const {
    return config_["wbc_model"].toObject()["default_model_path"].toString();
}

QStringList ConfigManager::getWBCClasses() const {
    QStringList classes;
    QJsonArray classArray = config_["wbc_model"].toObject()["classes"].toArray();
    for (const QJsonValue& value : classArray) {
        classes.append(value.toString());
    }
    return classes;
}

QString ConfigManager::getRBCModelPath() const {
    return config_["rbc_model"].toObject()["default_model_path"].toString();
}

QStringList ConfigManager::getRBCClasses() const {
    QStringList classes;
    QJsonArray classArray = config_["rbc_model"].toObject()["classes"].toArray();
    for (const QJsonValue& value : classArray) {
        classes.append(value.toString());
    }
    return classes;
}

QString ConfigManager::getPltModelPath() const {
    return config_["plt_model"].toObject()["default_model_path"].toString();
}

QStringList ConfigManager::getPltClasses() const {
    QStringList classes;
    QJsonArray classArray = config_["plt_model"].toObject()["classes"].toArray();
    for (const QJsonValue& value : classArray) {
        classes.append(value.toString());
    }
    return classes;
}

float ConfigManager::getConfidenceThreshold() const {
    return config_["detection"].toObject()["confidence_threshold"].toDouble();
}

float ConfigManager::getNmsThreshold() const {
    return config_["detection"].toObject()["nms_threshold"].toDouble();
}

bool ConfigManager::getSaveResults() const {
    return config_["detection"].toObject()["save_results"].toBool();
}

bool ConfigManager::getSaveTxtResults() const {
    return config_["detection"].toObject()["save_txt_results"].toBool();
}

bool ConfigManager::getUseFixedSize() const {
    return config_["detection"].toObject()["use_fixed_size"].toBool();
}

int ConfigManager::getFixedWidth() const {
    return config_["detection"].toObject()["fixed_width"].toInt();
}

int ConfigManager::getFixedHeight() const {
    return config_["detection"].toObject()["fixed_height"].toInt();
}

int ConfigManager::getMaxDisplayPerCategory() const {
    return config_["detection"].toObject()["max_display_per_category"].toInt(500); // 默认值500
}

bool ConfigManager::getFilterBoundaryTargets() const {
    return config_["detection"].toObject()["filter_boundary_targets"].toBool(false); // 默认值false
}

int ConfigManager::getBoundaryThreshold() const {
    return config_["detection"].toObject()["boundary_threshold"].toInt(20); // 默认值20像素
}

QString ConfigManager::getWBCResultsBaseDir() const {
    return dynamicBaseDir_ + "/WBC";
}

QString ConfigManager::getWBCCropDir() const {
    return dynamicBaseDir_ + "/WBC/results_crop";
}

QString ConfigManager::getWBCTxtDir() const {
    return dynamicBaseDir_ + "/WBC/results_txt";
}

QString ConfigManager::getWBCJsonDir() const {
    return dynamicBaseDir_ + "/WBC/results_json";
}

QString ConfigManager::getWBCCsvDir() const {
    return dynamicBaseDir_ + "/WBC/results_csv";
}

int ConfigManager::getDefaultImageWidth() const {
    return config_["annotation"].toObject()["default_image_width"].toInt();
}

int ConfigManager::getDefaultImageHeight() const {
    return config_["annotation"].toObject()["default_image_height"].toInt();
}

float ConfigManager::getIouThreshold() const {
    return config_["validation"].toObject()["iou_threshold"].toDouble();
}

// RBC 路径方法
QString ConfigManager::getRBCResultsBaseDir() const {
    return dynamicBaseDir_ + "/RBC";
}

QString ConfigManager::getRBCCropDir() const {
    return dynamicBaseDir_ + "/RBC/results_crop";
}

QString ConfigManager::getRBCTxtDir() const {
    return dynamicBaseDir_ + "/RBC/results_txt";
}

QString ConfigManager::getRBCJsonDir() const {
    return dynamicBaseDir_ + "/RBC/results_json";
}

QString ConfigManager::getRBCCsvDir() const {
    return dynamicBaseDir_ + "/RBC/results_csv";
}

// PLT 路径方法
QString ConfigManager::getPltResultsBaseDir() const {
    return dynamicBaseDir_ + "/PLT";
}

QString ConfigManager::getPltCropDir() const {
    return dynamicBaseDir_ + "/PLT/results_crop";
}

QString ConfigManager::getPltTxtDir() const {
    return dynamicBaseDir_ + "/PLT/results_txt";
}

QString ConfigManager::getPltJsonDir() const {
    return dynamicBaseDir_ + "/PLT/results_json";
}

QString ConfigManager::getPltCsvDir() const {
    return dynamicBaseDir_ + "/PLT/results_csv";
}

// HGB 配置方法
float ConfigManager::getHgbCalibrationCoef() const {
    return config_["hgb_config"].toObject()["default_calibration_coef"].toObject()["hgb"].toDouble(1.0);
}

float ConfigManager::getMcvCalibrationCoef() const {
    return config_["hgb_config"].toObject()["default_calibration_coef"].toObject()["mcv"].toDouble(1.0);
}

float ConfigManager::getPltCalibrationCoef() const {
    return config_["hgb_config"].toObject()["default_calibration_coef"].toObject()["plt"].toDouble(1.0);
}

float ConfigManager::getRbcCalibrationCoef() const {
    return config_["hgb_config"].toObject()["default_calibration_coef"].toObject()["rbc"].toDouble(1.0);
}

float ConfigManager::getWbcCalibrationCoef() const {
    return config_["hgb_config"].toObject()["default_calibration_coef"].toObject()["wbc"].toDouble(1.0);
}

bool ConfigManager::getSelectBaso() const {
    return config_["hgb_config"].toObject()["channel_selection"].toObject()["select_baso"].toBool(true);
}

bool ConfigManager::getSelectDiff() const {
    return config_["hgb_config"].toObject()["channel_selection"].toObject()["select_diff"].toBool(true);
}

bool ConfigManager::getSelectNrbc() const {
    return config_["hgb_config"].toObject()["channel_selection"].toObject()["select_nrbc"].toBool(true);
}

bool ConfigManager::getSelectRet() const {
    return config_["hgb_config"].toObject()["channel_selection"].toObject()["select_ret"].toBool(true);
}

bool ConfigManager::getSelectCbc() const {
    return config_["hgb_config"].toObject()["channel_selection"].toObject()["select_cbc"].toBool(true);
}

bool ConfigManager::getEnableCrcCheck() const {
    return config_["hgb_config"].toObject()["data_validation"].toObject()["enable_crc_check"].toBool(true);
}

int ConfigManager::getMaxFileSizeMb() const {
    return config_["hgb_config"].toObject()["data_validation"].toObject()["max_file_size_mb"].toInt(100);
}

int ConfigManager::getMinMeasurementPoints() const {
    return config_["hgb_config"].toObject()["data_validation"].toObject()["min_measurement_points"].toInt(1);
}

int ConfigManager::getMaxMeasurementPoints() const {
    return config_["hgb_config"].toObject()["data_validation"].toObject()["max_measurement_points"].toInt(100000);
}

// HGB 路径方法
QString ConfigManager::getHgbResultsBaseDir() const {
    return dynamicBaseDir_ + "/HGB";
}

QString ConfigManager::getHgbCsvDir() const {
    return dynamicBaseDir_ + "/HGB/results_csv";
}

QString ConfigManager::getHgbLogDir() const {
    return dynamicBaseDir_ + "/HGB/logs";
}

void ConfigManager::generateNewTimestampPaths() {
    // 生成当前时间戳
    currentTimestamp_ = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");

    // 生成动态基础目录
    dynamicBaseDir_ = QString("results_%1").arg(currentTimestamp_);
}

void ConfigManager::updateTimestamp() {
    // 手动更新时间戳
    generateNewTimestampPaths();
    utils::Logger::instance().info(QString("时间戳已更新: %1").arg(currentTimestamp_));
    utils::Logger::instance().info(QString("新的保存路径: %1").arg(dynamicBaseDir_));
}

} // namespace utils