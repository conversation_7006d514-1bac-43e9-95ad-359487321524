#ifndef EXCEL_EXPORTER_H
#define EXCEL_EXPORTER_H

#include <string>
#include <vector>
#include <tuple>

// 前向声明Detection结构体
struct Detection;

namespace utils {
namespace ExcelExporter {

// 导出检测结果到Excel文件
bool exportDetections(const std::vector<std::tuple<std::string, Detection>>& results, const std::string& filepath);
bool exportCountsWithAllFiles(const std::vector<std::tuple<std::string, Detection>>& results,
                              const std::vector<std::string>& allFiles,
                              const std::string& filepath);

} // namespace ExcelExporter
} // namespace utils

#endif // EXCEL_EXPORTER_H