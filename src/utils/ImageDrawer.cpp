#include "utils/ImageDrawer.h"
#include "utils/Logger.h"
#include <QString>
#include <opencv2/imgproc.hpp>

namespace utils {
namespace ImageDrawer {

// 使用Scalar颜色参数的边界框绘制函数
void drawBoundingBox(cv::Mat& image, const cv::Rect& box, const cv::Scalar& color, int thickness) {
    if (image.empty()) return;

    // 绘制矩形框
    cv::rectangle(image, box, color, thickness);
}

// 为了向后兼容保留的RGB参数版本
void drawBoundingBox(cv::Mat& image, const cv::Rect& box, int r, int g, int b, int thickness) {
    drawBoundingBox(image, box, cv::Scalar(b, g, r), thickness);
}

// 使用Scalar颜色参数的文本绘制函数
void drawText(cv::Mat& image, const std::string& text, const cv::Point& position, const cv::Scalar& color, float fontScale, int thickness) {
    if (image.empty()) return;

    // 添加文本标签
    cv::putText(image,
                text,
                position,
                cv::FONT_HERSHEY_SIMPLEX,
                fontScale,
                color,
                thickness);
}

// 带背景的文本绘制函数
void drawTextWithBackground(cv::Mat& image, const std::string& text, const cv::Point& position,
                           const cv::Scalar& textColor, const cv::Scalar& bgColor,
                           float fontScale, int thickness, bool belowRect) {
    if (image.empty()) return;

    // 获取文本大小
    int baseline;
    cv::Size textSize = cv::getTextSize(text, cv::FONT_HERSHEY_SIMPLEX, fontScale*1.2, thickness, &baseline);

    // 计算文本位置
    cv::Point textPos = position;
    if (belowRect) {
        // 如果需要在框左下角以下显示，调整y坐标
        // 将y坐标设置为边界框的底部加上一些间距
        textPos.y = position.y + textSize.height; // position.y应该是边界框的底部坐标
    }
    else {
        textPos.y = position.y - baseline;
    }

    // 创建背景矩形
    cv::Rect bgRect(textPos.x, textPos.y - textSize.height, textSize.width, textSize.height + baseline);

    // 检查并调整文本位置，确保不超出图像边界
    // 检查左边界
    if (bgRect.x < 0) {
        int shift = -bgRect.x;
        bgRect.x = 0;
        textPos.x += shift;
    }

    // 检查上边界
    if (bgRect.y < 0) {
        int shift = -bgRect.y;
        bgRect.y = 0;
        textPos.y += shift;
    }

    // 检查右边界
    if (bgRect.x + bgRect.width > image.cols) {
        int overflow = (bgRect.x + bgRect.width) - image.cols;
        bgRect.x = bgRect.x - overflow;
        textPos.x = textPos.x - overflow;
    }

    // 检查下边界
    if (bgRect.y + bgRect.height > image.rows) {
        int overflow = (bgRect.y + bgRect.height) - image.rows;
        // 如果是在边界框下方显示，尝试移到边界框上方
        if (belowRect) {
            // 移到边界框上方
            bgRect.y = position.y - textSize.height - baseline - 5;
            textPos.y = position.y - 5;

            // 再次检查上边界
            if (bgRect.y < 0) {
                bgRect.y = 0;
                textPos.y = baseline + 5;
            }
        } else {
            // 直接调整高度
            bgRect.height = image.rows - bgRect.y;
        }
    }

    // 绘制背景矩形
    cv::rectangle(image, bgRect, bgColor, cv::FILLED);

    // 绘制文本
    cv::putText(image,
                text,
                textPos,
                cv::FONT_HERSHEY_SIMPLEX,
                fontScale,
                textColor,
                thickness);
}

} // namespace ImageDrawer
} // namespace utils