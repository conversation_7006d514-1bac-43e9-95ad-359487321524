#ifndef LOGGER_H
#define LOGGER_H

#include <QString>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QObject>
#include <functional>
#include <memory>
#include <mutex>
#include <vector>

namespace utils {

class Logger : public QObject {
    Q_OBJECT
public:
    enum class Level {
        Debug,
        Warning,
        Error,
        Info,
    };

    using LogCallback = std::function<void(Level, const QString&)>;

    static Logger& instance();

    // 基本日志接口
    void debug(const QString& message);
    void warning(const QString& message);
    void error(const QString& message);
    void info(const QString& message);

    // 日志回调注册
    void registerCallback(LogCallback callback);
    void clearCallback(LogCallback callback);

    // 文件日志设置
    bool setLogFile(const QString& filePath);
    void enableFileLogging(bool enable);
    void setLogLevel(Level level);

private:
    Logger();
    ~Logger();
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    void log(Level level, const QString& message);
    QString levelToString(Level level) const;

    Level currentLevel_;
    std::vector<LogCallback> callbacks_;
    std::unique_ptr<QFile> logFile_;
    std::unique_ptr<QTextStream> logStream_;
    std::mutex mutex_;
    bool fileLoggingEnabled_;
};

} // namespace utils

#endif // LOGGER_H