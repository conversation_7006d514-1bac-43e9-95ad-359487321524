#pragma once

#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <QFile>
#include <QDir>
#include "Logger.h"

namespace utils {

class ConfigManager {
public:
    static ConfigManager& instance();

    // 初始化配置
    bool initialize(const QString& configPath = "config/default_config.json");

    // WBC相关配置
    QString getWBCModelPath() const;
    QStringList getWBCClasses() const;

    // RBC相关配置
    QString getRBCModelPath() const;
    QStringList getRBCClasses() const;

    // PLT相关配置
    QString getPltModelPath() const;
    QStringList getPltClasses() const;
    
    float getConfidenceThreshold() const;
    float getNmsThreshold() const;
    bool getSaveResults() const;
    bool getSaveTxtResults() const;
    bool getUseFixedSize() const;
    int getFixedWidth() const;
    int getFixedHeight() const;
    int getMaxDisplayPerCategory() const;
    bool getFilterBoundaryTargets() const;
    int getBoundaryThreshold() const;
    
    QString getWBCResultsBaseDir() const;
    QString getWBCCropDir() const;
    QString getWBCTxtDir() const;
    QString getWBCJsonDir() const;
    QString getWBCCsvDir() const;

    // RBC路径相关
    QString getRBCResultsBaseDir() const;
    QString getRBCCropDir() const;
    QString getRBCTxtDir() const;
    QString getRBCJsonDir() const;
    QString getRBCCsvDir() const;

    // PLT路径相关
    QString getPltResultsBaseDir() const;
    QString getPltCropDir() const;
    QString getPltTxtDir() const;
    QString getPltJsonDir() const;
    QString getPltCsvDir() const;

    // HGB配置相关
    float getHgbCalibrationCoef() const;
    float getMcvCalibrationCoef() const;
    float getPltCalibrationCoef() const;
    float getRbcCalibrationCoef() const;
    float getWbcCalibrationCoef() const;

    bool getSelectBaso() const;
    bool getSelectDiff() const;
    bool getSelectNrbc() const;
    bool getSelectRet() const;
    bool getSelectCbc() const;

    bool getEnableCrcCheck() const;
    int getMaxFileSizeMb() const;
    int getMinMeasurementPoints() const;
    int getMaxMeasurementPoints() const;

    // HGB路径相关
    QString getHgbResultsBaseDir() const;
    QString getHgbCsvDir() const;
    QString getHgbLogDir() const;

    // 动态路径管理
    void generateNewTimestampPaths();
    void updateTimestamp(); // 手动更新时间戳
    int getDefaultImageWidth() const;
    int getDefaultImageHeight() const;
    float getIouThreshold() const;

private:
    ConfigManager() = default;
    ~ConfigManager() = default;
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;

    QJsonObject config_;
    QString currentTimestamp_;  // 当前时间戳
    QString dynamicBaseDir_;    // 动态生成的基础目录
};

} // namespace utils