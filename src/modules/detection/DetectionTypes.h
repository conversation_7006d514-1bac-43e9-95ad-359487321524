#ifndef DETECTION_TYPES_H
#define DETECTION_TYPES_H

#include <opencv2/core/core.hpp>
#include <string>
#include <map>
#include <vector>

// Letterbox信息结构体
struct LetterBoxInfo {
    float scale;
    int pad_left;
    int pad_top;
};

// 检测结果结构体
struct Detection {
    cv::Rect bbox;
    std::string label;
    float confidence;
    std::string imagePath; // 添加图像路径字段，用于在检测结果面板中显示图像路径信息
};

// 每个类别的评估指标
struct ClassMetrics {
    std::string className;
    float precision;    // 精确率
    float recall;       // 召回率
    float f1Score;      // F1分数
    float ap;           // 平均精度 (AP)
    float ap50;         // AP at IoU=0.50
    float ap75;         // AP at IoU=0.75
    int TP;             // 真阳性数量
    int FP;             // 假阳性数量
    int FN;             // 假阴性数量
    int sampleCount;    // 该类别的样本总数 (support)
    int imageCount;     // 包含该类别的图像数量
    int predictionCount; // 预测为该类别的数量
};

// 评估指标结构
struct EvaluationMetrics {
    // 总体指标
    float precision;     // 总体精确率
    float recall;        // 总体召回率
    float f1Score;       // 总体F1分数
    float accuracy;      // 总体准确率
    float mAP;           // 平均mAP (0.5-0.95)
    float mAP50;         // mAP at IoU=0.50
    float mAP75;         // mAP at IoU=0.75
    std::vector<float> mAPs; // 不同IoU阈值下的mAP值 (0.5, 0.55, ..., 0.95)
    int TP;              // 总真阳性数量
    int FP;              // 总假阳性数量
    int FN;              // 总假阴性数量
    int totalSamples;    // 样本总数
    int totalImages;     // 图像总数
    int totalPredictions; // 预测总数
    float inferenceTime; // 推理时间 (ms)
    float preprocessTime; // 预处理时间 (ms)
    float postprocessTime; // 后处理时间 (ms)

    // 每个类别的详细指标
    std::vector<ClassMetrics> perClassMetrics;

    // 混淆矩阵相关
    std::map<std::string, std::map<std::string, int>> confusionMatrix; // 格式: {真实类别: {预测类别: 计数}}
    std::vector<std::string> classNames; // 所有出现的类别名称
};

#endif // DETECTION_TYPES_H