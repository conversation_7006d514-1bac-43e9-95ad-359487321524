#include "modules/detection/YoloAlgorithm.h"
#include <QFileInfo>
#include <QFile>
#include <QDir>
#include <QElapsedTimer>
#include <QDateTime>
#include <filesystem>
#include <set>
#include <algorithm>
#include <map>
#include <cstdlib>
#include "utils/ConfigManager.h"
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>

YoloAlgorithm::YoloAlgorithm(AlgorithmType type) :
    algorithmType_(type),
    modelLoaded_(false),
    filterBoundaryTargets_(false),
    boundaryThreshold_(20) {
}

bool YoloAlgorithm::loadModel(const std::string &modelPath) {
    modelLoaded_ = yolo_.loadModel(modelPath);
    return modelLoaded_;
}

bool YoloAlgorithm::loadClassNames(const QStringList &classes) {
    return yolo_.loadClassNames(classes);
}

void YoloAlgorithm::setCurrentImagePath(const QString& path) {
    currentImagePath_ = path;
}

bool YoloAlgorithm::predict(const cv::Mat &input) {
    if (!isModelLoaded()) {
        utils::Logger::instance().error("模型未加载");
        return false;
    }

    if (input.empty()) {
        utils::Logger::instance().error("输入图像为空");
        return false;
    }

    // 使用 YoloRunner 的实际模型输入尺寸
    cv::Size modelSize(yolo_.getInputWidth(), yolo_.getInputHeight());

    std::vector<cv::Rect> boxes;
    std::vector<std::string> labels;
    std::vector<float> scores;

    // 调用infer方法，现在返回bool值
    if (!yolo_.infer(input, boxes, labels, scores)) {
        utils::Logger::instance().error("推理失败");
        return false;
    }

    // 将检测结果转换为 Detection 结构
    QList<Detection> detections;
    float scaleX = static_cast<float>(input.cols) / modelSize.width;
    float scaleY = static_cast<float>(input.rows) / modelSize.height;

    // 初始化剔除计数器
    int currentExcludedCount = 0;

    for (size_t i = 0; i < boxes.size(); ++i) {
        if (scores[i] >= confidenceThreshold_) {
            Detection det;
            det.bbox = boxes[i];
            det.label = labels[i];
            det.confidence = scores[i];

            // 如果启用边界目标过滤，检查目标是否超出边界太多
            if (filterBoundaryTargets_) {
                // 使用配置的边界阈值：目标中心点距离图像边界的最小距离（像素）
                const int boundaryThreshold = boundaryThreshold_;

                // 计算目标中心点
                int centerX = det.bbox.x + det.bbox.width / 2;
                int centerY = det.bbox.y + det.bbox.height / 2;

                // 检查中心点是否距离边界太近
                bool tooCloseToEdge = (centerX < boundaryThreshold) ||
                                     (centerX > input.cols - boundaryThreshold) ||
                                     (centerY < boundaryThreshold) ||
                                     (centerY > input.rows - boundaryThreshold);

                // 如果目标距离边界太近，跳过该目标
                if (tooCloseToEdge) {
                    currentExcludedCount++;
                    utils::Logger::instance().debug(QString("过滤边界目标: 中心点(%1,%2), 图像尺寸(%3,%4)")
                        .arg(centerX).arg(centerY).arg(input.cols).arg(input.rows));
                    continue;
                }
            }

            detections.append(det);
        }
    }

    lastExcludedCount_ = currentExcludedCount;
    if (filterBoundaryTargets_ && currentExcludedCount > 0) {
        utils::Logger::instance().info(QString("本张图像剔除 %1 个边缘目标").arg(currentExcludedCount));
    }

    utils::Logger::instance().info(QString("正在处理图像: %1").arg(currentImagePath_));

    // 统计每个类别的检测总数
    std::map<std::string, int> classCountMap;
    for (const auto& det : detections) {
        classCountMap[det.label]++;
    }
    for (const auto& [className, count] : classCountMap) {
        utils::Logger::instance().info(QString("检测到 %1 个 %2").arg(count).arg(QString::fromStdString(className)));
    }

    // 保存预测结果
    if (!currentImagePath_.isEmpty()) {
        predictedResults_[currentImagePath_] = detections;

        // 保存目标裁切结果（如果启用）
        if (!detections.empty() && saveResults_) {
            saveCroppedObjects(currentImagePath_, input, detections);
        }

        // 保存TXT检测结果（如果启用）
        if (!detections.empty() && saveTxtResults_) {
            saveTxtDetections(currentImagePath_, detections);
        }

        // 保存JSON检测结果（如果启用）
        if (!detections.empty() && saveJsonResults_) {
            saveJsonDetections(currentImagePath_, detections);
        }
    }

    return true;
}

bool YoloAlgorithm::batchPredict(const QStringList &imagePaths) {
    // 基本验证
    if (imagePaths.isEmpty()) {
        utils::Logger::instance().warning("批处理图像路径列表为空");
        return false;
    }

    // 记录批处理开始
    utils::Logger::instance().info(QString("开始批量处理 %1 张图像").arg(imagePaths.size()));
    QElapsedTimer timer;
    timer.start();

    // 准备批处理
    int totalImages = imagePaths.size();
    int processedCount = 0;
    int batchTotalExcludedCount = 0;
    batchDetectionsForExport_.clear();
    batchProcessedFiles_.clear();

    QString previousPath = currentImagePath_;  // 保存当前路径以便恢复

    // 处理每张图像
    for (int i = 0; i < totalImages; ++i) {
        const QString& path = imagePaths[i];

        // 加载图像
        cv::Mat img = utils::ImageIO::imageRead(path);
        if (img.empty()) {
            utils::Logger::instance().warning(QString("无法加载图像: %1，跳过").arg(path));
            continue;
        }

        // 设置当前图像路径并处理
        currentImagePath_ = path;
        QFileInfo fileInfo(path);

        if (predict(img)) {
            // 累加单张图的剔除数量
            if (filterBoundaryTargets_) {
                batchTotalExcludedCount += lastExcludedCount_;
            }
            // 记录处理过的文件名（无论是否有检测结果）
            batchProcessedFiles_.push_back(fileInfo.fileName().toStdString());

            // 获取检测结果并保存
            QList<Detection> detections = predictedResults_[path];

            // 收集检测结果用于导出
            for (const auto& det : detections) {
                batchDetectionsForExport_.push_back(std::make_tuple(
                    fileInfo.fileName().toStdString(), det
                ));
            }

            processedCount++;
        } else {
            utils::Logger::instance().warning(QString("处理图像失败: %1").arg(path));
        }

        // 更新进度
        if (progressCallback_) {
            progressCallback_(i + 1, totalImages);
        }
    }

    // 恢复之前的图像路径
    currentImagePath_ = previousPath;

    // 统计每个类别的检测总数
    std::map<std::string, int> classCountMap;
    for (const auto& item : batchDetectionsForExport_) {
        const Detection& det = std::get<1>(item);
        classCountMap[det.label]++;
    }

    // 记录批处理完成
    qint64 elapsed = timer.elapsed();
    utils::Logger::instance().info(QString("批处理完成: 处理了 %1 张图像，耗时 %2 毫秒，平均每张 %3 毫秒")
        .arg(processedCount)
        .arg(elapsed)
        .arg(processedCount > 0 ? elapsed / processedCount : 0));

    // 输出每个类别的检测总数
    utils::Logger::instance().info("各类别检测总数:");
    for (const auto& [className, count] : classCountMap) {
        utils::Logger::instance().info(QString("  - %1: %2").arg(QString::fromStdString(className)).arg(count));
    }

    // 输出批处理剔除总数
    if (filterBoundaryTargets_) {
        utils::Logger::instance().info(QString("批处理共剔除 %1 个边缘目标").arg(batchTotalExcludedCount));
    }

    return true;
}

bool YoloAlgorithm::loadLabelmeGroundTruth(const QString &jsonPath) {
    QFile file(jsonPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        utils::Logger::instance().error("无法打开JSON标注文件: " + jsonPath);
        return false;
    }

    QByteArray jsonData = file.readAll();
    file.close();

    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (doc.isNull()) {
        utils::Logger::instance().error("JSON解析失败: " + jsonPath);
        return false;
    }

    QJsonObject jsonObj = doc.object();
    if (!jsonObj.contains("shapes") || !jsonObj["shapes"].isArray()) {
        utils::Logger::instance().error("无效的labelme格式: " + jsonPath);
        return false;
    }

    // 获取图像尺寸
    int imgWidth = jsonObj["imageWidth"].toInt();
    int imgHeight = jsonObj["imageHeight"].toInt();
    if (imgWidth <= 0 || imgHeight <= 0) {
        utils::Logger::instance().error("无效的图像尺寸: " + jsonPath);
        return false;
    }

    QList<Detection> detections;
    QJsonArray shapes = jsonObj["shapes"].toArray();

    for (const QJsonValue &shapeValue : shapes) {
        QJsonObject shape = shapeValue.toObject();
        if (shape["shape_type"].toString() != "rectangle") {
            continue;  // 跳过非矩形标注
        }

        Detection det;
        det.label = shape["label"].toString().toStdString();
        det.confidence = 1.0f;  // 标注数据默认置信度为1

        // 获取矩形框的点
        QJsonArray points = shape["points"].toArray();
        if (points.size() != 2) continue;

        // 获取左上角和右下角点
        QJsonArray topLeft = points[0].toArray();
        QJsonArray bottomRight = points[1].toArray();

        if (topLeft.size() != 2 || bottomRight.size() != 2) continue;

        // 获取原始像素坐标
        double x1 = topLeft[0].toDouble();
        double y1 = topLeft[1].toDouble();
        double x2 = bottomRight[0].toDouble();
        double y2 = bottomRight[1].toDouble();

        // 确保坐标顺序正确（左上角和右下角）
        if (x1 > x2) std::swap(x1, x2);
        if (y1 > y2) std::swap(y1, y2);

        // 将像素坐标归一化到0-1000范围
        det.bbox.x = static_cast<int>((x1 / imgWidth) * 1000);
        det.bbox.y = static_cast<int>((y1 / imgHeight) * 1000);
        det.bbox.width = static_cast<int>(((x2 - x1) / imgWidth) * 1000);
        det.bbox.height = static_cast<int>(((y2 - y1) / imgHeight) * 1000);

        detections.append(det);
    }

    // 使用图像文件名作为键来保存标注数据
    QFileInfo fileInfo(jsonPath);
    QString baseName = fileInfo.completeBaseName();
    groundTruthMap_[baseName] = detections;
    utils::Logger::instance().info(QString("加载标注: %1 (%2个)").arg(jsonPath).arg(detections.size()));

    return true;
}

bool YoloAlgorithm::loadYoloGroundTruth(const QString &gtPath) {
    QFile file(gtPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        utils::Logger::instance().error("无法打开标注文件: " + gtPath);
        return false;
    }

    QTextStream in(&file);
    QList<Detection> detections;
    
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.isEmpty()) continue;
        
        QStringList parts = line.split(" ");
        if (parts.size() < 5) continue;
        
        bool ok;
        Detection det;
        
        // 解析类别
        int classId = parts[0].toInt(&ok);
        if (ok && classId >= 0) {
            det.label = yolo_.getClassName(classId);
        } else {
            det.label = parts[0].toStdString();
        }
        
        // 解析归一化坐标
        float centerX = parts[1].toFloat(&ok);
        if (!ok || centerX < 0 || centerX > 1) continue;
        
        float centerY = parts[2].toFloat(&ok);
        if (!ok || centerY < 0 || centerY > 1) continue;
        
        float width = parts[3].toFloat(&ok);
        if (!ok || width <= 0 || width > 1) continue;
        
        float height = parts[4].toFloat(&ok);
        if (!ok || height <= 0 || height > 1) continue;
        
        // 保存原始的归一化坐标（使用1000倍放大来保持精度）
        det.bbox.x = static_cast<int>((centerX - width/2) * 1000);
        det.bbox.y = static_cast<int>((centerY - height/2) * 1000);
        det.bbox.width = static_cast<int>(width * 1000);
        det.bbox.height = static_cast<int>(height * 1000);
        
        det.confidence = 1.0f;  // 标注数据置信度为1
        detections.append(det);
    }
    
    file.close();
    
    // 使用图像文件名（不含扩展名）作为key存储标注数据
    QFileInfo fileInfo(gtPath);
    QString baseName = fileInfo.completeBaseName();
    groundTruthMap_[baseName] = detections;
    utils::Logger::instance().info(QString("加载标注: %1 (%2个)").arg(gtPath).arg(detections.size()));
    
    return true;
}

bool YoloAlgorithm::loadGroundTruthBatch(const QStringList &gtPaths) {
    if (gtPaths.isEmpty()) {
        return false;
    }

    // 预先清除现有标注数据
    groundTruthMap_.clear();

    // 按文件类型分组处理，减少文件类型判断次数
    QStringList txtFiles, jsonFiles, otherFiles;
    for (const QString& path : gtPaths) {
        QFileInfo fileInfo(path);
        QString suffix = fileInfo.suffix().toLower();
        if (suffix == "txt") {
            txtFiles.append(path);
        } else if (suffix == "json") {
            jsonFiles.append(path);
        } else {
            otherFiles.append(path);
        }
    }

    // 统计加载结果
    int successCount = 0;
    int totalFiles = gtPaths.size();

    // 批量处理TXT文件
    for (const QString& path : txtFiles) {
        if (loadYoloGroundTruth(path)) {
            successCount++;
        }
    }

    // 批量处理JSON文件
    for (const QString& path : jsonFiles) {
        if (loadLabelmeGroundTruth(path)) {
            successCount++;
        }
    }

    // 处理其他类型文件（如果有）
    for (const QString& path : otherFiles) {
        utils::Logger::instance().error("不支持的标注文件格式: " + path);
    }

    // 只记录一次总体结果
    if (successCount > 0) {
        utils::Logger::instance().info(QString("批量加载标注文件完成: %1/%2").arg(successCount).arg(totalFiles));
    }

    return successCount > 0;
}

void YoloAlgorithm::clearGroundTruths() {
    groundTruthMap_.clear();
}

bool YoloAlgorithm::getPredictionsForImage(const QString &imagePath, QList<Detection> &detections) {
    // 尝试通过完整路径获取预测结果
    if (predictedResults_.contains(imagePath)) {
        detections = predictedResults_[imagePath];
        return true;
    }

    // 尝试通过文件名匹配
    QFileInfo fileInfo(imagePath);
    QString baseName = fileInfo.completeBaseName();

    for (auto it = predictedResults_.begin(); it != predictedResults_.end(); ++it) {
        QString itBaseName = QFileInfo(it.key()).completeBaseName();

        if (itBaseName == baseName) {
            detections = it.value();
            return true;
        }
    }

    // 未找到匹配的预测结果
    return false;
}

const QMap<QString, QList<Detection>>& YoloAlgorithm::getAllPredictions() const {
    return predictedResults_;
}

const QMap<QString, QList<Detection>>& YoloAlgorithm::getAllGroundTruth() const {
    return groundTruthMap_;
}

void YoloAlgorithm::clearPredictions() {
    predictedResults_.clear();
    batchDetectionsForExport_.clear();
}

bool YoloAlgorithm::exportBatchPredictions() {
    if (batchDetectionsForExport_.empty()) {
        return false;
    }

    // 根据算法类型选择基础保存目录路径
    QString baseDirPath = getResultsBaseDir();

    QString csvDirPath = baseDirPath;
    QDir csvDir(csvDirPath);

    // 创建CSV保存目录
    if (!csvDir.exists()) {
        if (!csvDir.mkpath(".")) {
            utils::Logger::instance().warning(QString("无法创建csv保存目录: %1").arg(csvDirPath));
            return false;
        }
    }

    // 根据算法类型选择CSV文件名
    QString csvFileName, countCsvFileName;
    csvFileName = "/detections.csv";
    countCsvFileName = "/counts.csv";

    bool detExported = utils::ExcelExporter::exportDetections(
        batchDetectionsForExport_,
        (csvDirPath + csvFileName).toStdString()
    );

    bool countExported = utils::ExcelExporter::exportCountsWithAllFiles(
        batchDetectionsForExport_,
        batchProcessedFiles_,
        (csvDirPath + countCsvFileName).toStdString()
    );

    return detExported && countExported;
}

bool YoloAlgorithm::saveTxtDetections(const QString &imagePath, const QList<Detection> &detections) {
    if (detections.isEmpty()) {
        utils::Logger::instance().debug("没有检测结果可保存为TXT");
        return false;
    }

    // 根据算法类型选择保存路径
    QString txtDirPath = getTxtDir();
    QDir txtDir(txtDirPath);
    if (!txtDir.exists()) {
        if (!txtDir.mkpath(".")) {
            utils::Logger::instance().warning(QString("无法创建TXT保存目录: %1").arg(txtDirPath));
            return false;
        }
    }

    QFileInfo fileInfo(imagePath);
    QString outputPath = txtDirPath + "/" + fileInfo.completeBaseName() + ".txt";

    // 创建TXT文件
    QFile file(outputPath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        utils::Logger::instance().error("无法创建TXT文件: " + outputPath);
        return false;
    }

    QTextStream out(&file);

    // 获取图像尺寸，用于归一化坐标
    cv::Mat img = utils::ImageIO::imageRead(imagePath);
    if (img.empty()) {
        utils::Logger::instance().warning("无法加载图像获取尺寸: " + imagePath);
        file.close();
        return false;
    }

    int imgWidth = img.cols;
    int imgHeight = img.rows;

    // 写入检测结果，YOLO格式: <class_id> <x_center> <y_center> <width> <height>
    for (const Detection& det : detections) {
        // 获取类别ID
        int classId = -1;
        const auto& classNames = yolo_.getClassNames();
        for (size_t i = 0; i < classNames.size(); i++) {
            if (det.label == classNames[i]) {
                classId = static_cast<int>(i);
                break;
            }
        }

        // 如果找不到类别ID，使用-1
        if (classId == -1) {
            utils::Logger::instance().warning(QString("未知类别: %1，使用类别名称").arg(QString::fromStdString(det.label)));
            out << QString::fromStdString(det.label) << " ";
        } else {
            out << classId << " ";
        }

        // 计算归一化的中心点坐标和宽高
        float centerX = (det.bbox.x + det.bbox.width / 2.0f) / imgWidth;
        float centerY = (det.bbox.y + det.bbox.height / 2.0f) / imgHeight;
        float width = static_cast<float>(det.bbox.width) / imgWidth;
        float height = static_cast<float>(det.bbox.height) / imgHeight;

        // 写入归一化坐标
        out << QString::number(centerX, 'f', 6) << " "
            << QString::number(centerY, 'f', 6) << " "
            << QString::number(width, 'f', 6) << " "
            << QString::number(height, 'f', 6) << "\n";
    }

    file.close();
    return true;
}

bool YoloAlgorithm::saveCroppedObjects(const QString &imagePath, const cv::Mat &image, const QList<Detection> &detections) {
    if (detections.isEmpty()) {
        utils::Logger::instance().debug("没有检测结果可裁切保存");
        return false;
    }

    // 根据算法类型选择保存路径
    QString cropDirPath = getCropDir();
    QDir cropDir(cropDirPath);
    if (!cropDir.exists()) {
        if (!cropDir.mkpath(".")) {
            utils::Logger::instance().warning(QString("无法创建裁切图像保存目录: %1").arg(cropDirPath));
            return false;
        }
    }

    QFileInfo fileInfo(imagePath);
    // 使用completeBaseName()而不是baseName()来保留文件名中的所有点
    QString baseName = fileInfo.completeBaseName();

    // 记录成功保存的数量
    int savedCount = 0;

    // 按类别分组保存裁切图像
    for (const Detection& det : detections) {
        // 创建类别目录
        QString categoryDir = cropDirPath + "/" + QString::fromStdString(det.label);
        QDir dir(categoryDir);
        if (!dir.exists()) {
            if (!dir.mkpath(".")) {
                utils::Logger::instance().warning(QString("无法创建类别目录: %1").arg(categoryDir));
                continue;
            }
        }

        // 计算裁切区域
        cv::Rect cropRect;
        if (useFixedSize_) {
            // 计算目标中心点
            int centerX = det.bbox.x + det.bbox.width / 2;
            int centerY = det.bbox.y + det.bbox.height / 2;

            // 以中心点为基准计算固定宽高的裁切区域
            cropRect = cv::Rect(
                centerX - fixedWidth_ / 2,
                centerY - fixedHeight_ / 2,
                fixedWidth_,
                fixedHeight_
            );

            // 确保裁切区域在图像范围内
            cropRect.x = std::max(0, std::min(cropRect.x, image.cols - cropRect.width));
            cropRect.y = std::max(0, std::min(cropRect.y, image.rows - cropRect.height));

            // 如果裁切区域超出图像边界，调整大小
            if (cropRect.x + cropRect.width > image.cols) {
                cropRect.width = image.cols - cropRect.x;
            }
            if (cropRect.y + cropRect.height > image.rows) {
                cropRect.height = image.rows - cropRect.y;
            }
        } else {
            // 在ROI基础上向外扩展10个像素
            int expansion = 10;
            cropRect = cv::Rect(
                std::max(0, det.bbox.x - expansion),
                std::max(0, det.bbox.y - expansion),
                std::min(det.bbox.width + 2 * expansion, image.cols - std::max(0, det.bbox.x - expansion)),
                std::min(det.bbox.height + 2 * expansion, image.rows - std::max(0, det.bbox.y - expansion))
            );
        }

        // 检查裁切区域是否有效
        if (cropRect.width <= 0 || cropRect.height <= 0) {
            utils::Logger::instance().warning("忽略无效裁切区域");
            continue;
        }

        // 裁切图像
        cv::Mat cropped = image(cropRect).clone();

        // 生成输出文件名
        QString outputPath = categoryDir + "/" + baseName + "_" +
                             QString::number(savedCount) + "_" +
                             QString::fromStdString(det.label) + ".bmp";

        // 保存裁切图像，使用ImageIO来支持中文路径和tiff格式
        bool saved = utils::ImageIO::imageWrite(outputPath, cropped);
        if (saved) {
            savedCount++;
        } else {
            utils::Logger::instance().warning(QString("无法保存裁切图像: %1").arg(outputPath));
        }
    }

    return savedCount > 0;
}

float YoloAlgorithm::calculateIoU(const cv::Rect& box1, const cv::Rect& box2) {
    int xA = std::max(box1.x, box2.x);
    int yA = std::max(box1.y, box2.y);
    int xB = std::min(box1.x + box1.width, box2.x + box2.width);
    int yB = std::min(box1.y + box1.height, box2.y + box2.height);

    // 检查是否有重叠
    if (xB < xA || yB < yA) return 0.0f;

    float intersectionArea = (xB - xA) * (yB - yA);
    float box1Area = box1.width * box1.height;
    float box2Area = box2.width * box2.height;

    return intersectionArea / (box1Area + box2Area - intersectionArea);
}

QList<Detection> YoloAlgorithm::getGroundTruthForImage(const QString &imagePath) {
    QFileInfo fileInfo(imagePath);
    QString baseName = fileInfo.completeBaseName();

    // 直接匹配
    if (groundTruthMap_.contains(baseName)) {
        return groundTruthMap_[baseName];
    }

    // 如果没有找到匹配，返回空列表
    return QList<Detection>();
}

EvaluationMetrics YoloAlgorithm::validatePredictions() {
    EvaluationMetrics metrics;
    metrics.precision = 0.0f;
    metrics.recall = 0.0f;
    metrics.f1Score = 0.0f;
    metrics.accuracy = 0.0f;
    metrics.mAP = 0.0f;
    metrics.TP = 0;
    metrics.FP = 0;
    metrics.FN = 0;
    metrics.totalSamples = 0;
    metrics.totalImages = 0;
    metrics.totalPredictions = 0;

    // 初始化混淆矩阵和类别相关数据
    metrics.confusionMatrix.clear();
    metrics.classNames.clear();
    metrics.perClassMetrics.clear();

    // 获取所有类别名称
    std::set<std::string> uniqueClassNames;
    for (const auto& className : yolo_.getClassNames()) {
        uniqueClassNames.insert(className);
    }

    if (groundTruthMap_.isEmpty()) {
        utils::Logger::instance().warning("未加载任何标注数据，无法验证");
        return metrics;
    }

    // 用于计算总体指标
    int totalImages = 0;
    float totalPrecision = 0.0f;
    float totalRecall = 0.0f;

    // 用于计算每个类别的指标
    std::map<std::string, int> classTP;
    std::map<std::string, int> classFP;
    std::map<std::string, int> classFN;
    std::map<std::string, int> classSampleCount;
    std::map<std::string, std::set<QString>> classImageSet;
    std::map<std::string, int> classPredictionCount;

    // 对每张有标注的图像进行验证
    for (auto it = groundTruthMap_.begin(); it != groundTruthMap_.end(); ++it) {
        QString imageName = it.key();
        QList<Detection> groundTruth = it.value();

        // 查找对应的预测结果
        QString imagePath;
        for (const QString& path : predictedResults_.keys()) {
            if (QFileInfo(path).completeBaseName() == imageName) {
                imagePath = path;
                break;
            }
        }

        // 处理未找到预测结果的情况
        if (imagePath.isEmpty() || !predictedResults_.contains(imagePath)) {
            utils::Logger::instance().warning("未找到对应的预测结果: " + imageName);

            // 记录为假阴性(FN)
            for (const Detection& det : groundTruth) {
                classSampleCount[det.label]++;
                metrics.totalSamples++;
                classImageSet[det.label].insert(imageName);
                metrics.confusionMatrix["background"][det.label]++;
                classFN[det.label]++;
                metrics.FN++;
                uniqueClassNames.insert(det.label);
                uniqueClassNames.insert("background");
            }
            continue;
        }

        // 获取预测结果
        QList<Detection> predictions = predictedResults_[imagePath];

        // 统计预测数量
        for (const Detection& pred : predictions) {
            classPredictionCount[pred.label]++;
            metrics.totalPredictions++;
        }

        // 加载图像以获取尺寸
        cv::Mat img = utils::ImageIO::imageRead(imagePath);
        if (img.empty()) {
            utils::Logger::instance().warning("无法加载图像: " + imagePath);
            continue;
        }

        // 转换坐标
        int imgWidth = img.cols;
        int imgHeight = img.rows;
        for (Detection& det : groundTruth) {
            det.bbox.x = static_cast<int>((det.bbox.x / 1000.0f) * imgWidth);
            det.bbox.y = static_cast<int>((det.bbox.y / 1000.0f) * imgHeight);
            det.bbox.width = static_cast<int>((det.bbox.width / 1000.0f) * imgWidth);
            det.bbox.height = static_cast<int>((det.bbox.height / 1000.0f) * imgHeight);
            classSampleCount[det.label]++;
            metrics.totalSamples++;
            classImageSet[det.label].insert(imageName);
        }

        // 验证和过滤检测结果
        const std::vector<std::string>& validLabels = yolo_.getClassNames();
        std::set<std::string> validLabelSet(validLabels.begin(), validLabels.end());

        auto validateAndFilter = [&](QList<Detection>& detections, const QString& type) {
            for (int i = detections.size() - 1; i >= 0; --i) {
                if (validLabelSet.find(detections[i].label) == validLabelSet.end()) {
                    utils::Logger::instance().warning(QString("%1中包含未知类别: %2，将其忽略")
                        .arg(type)
                        .arg(QString::fromStdString(detections[i].label)));
                    detections.removeAt(i);
                }
            }
        };

        validateAndFilter(predictions, "预测结果");
        validateAndFilter(groundTruth, "标注数据");

        // 计算此图像的指标
        int TP = 0, FP = 0, FN = 0;
        QVector<bool> gtMatched(groundTruth.size(), false);
        QVector<bool> predMatched(predictions.size(), false);

        // 匹配真实目标和预测目标
        for (int i = 0; i < groundTruth.size(); ++i) {
            float bestIoU = 0.0f;
            int bestPredIndex = -1;

            for (int j = 0; j < predictions.size(); ++j) {
                if (!predMatched[j]) {
                    float iou = calculateIoU(groundTruth[i].bbox, predictions[j].bbox);
                    if (iou > bestIoU && iou > 0.5f) {
                        bestIoU = iou;
                        bestPredIndex = j;
                    }
                }
            }

            if (bestPredIndex >= 0) {
                const Detection& gt = groundTruth[i];
                const Detection& pred = predictions[bestPredIndex];
                gtMatched[i] = true;
                predMatched[bestPredIndex] = true;
                metrics.confusionMatrix[gt.label][pred.label]++;
                uniqueClassNames.insert(gt.label);
                uniqueClassNames.insert(pred.label);

                if (gt.label == pred.label) {
                    TP++;
                    classTP[pred.label]++;
                } else {
                    FP++;
                    classFP[pred.label]++;
                }
            }
        }

        // 处理未匹配的情况
        for (int i = 0; i < groundTruth.size(); ++i) {
            if (!gtMatched[i]) {
                FN++;
                const Detection& gt = groundTruth[i];
                classFN[gt.label]++;
                metrics.confusionMatrix[gt.label]["background"]++;
                uniqueClassNames.insert(gt.label);
                uniqueClassNames.insert("background");
            }
        }

        for (int j = 0; j < predictions.size(); ++j) {
            if (!predMatched[j]) {
                FP++;
                const Detection& pred = predictions[j];
                classFP[pred.label]++;
                metrics.confusionMatrix["background"][pred.label]++;
                uniqueClassNames.insert(pred.label);
                uniqueClassNames.insert("background");
            }
        }

        // 计算此图像的精度和召回率
        float precision = (TP + FP > 0) ? static_cast<float>(TP) / (TP + FP) : 0.0f;
        float recall = (TP + FN > 0) ? static_cast<float>(TP) / (TP + FN) : 0.0f;

        totalPrecision += precision;
        totalRecall += recall;
        totalImages++;

        metrics.TP += TP;
        metrics.FP += FP;
        metrics.FN += FN;
    }

    // 计算总体指标
    metrics.precision = (metrics.TP + metrics.FP > 0) ?
        static_cast<float>(metrics.TP) / (metrics.TP + metrics.FP) : 0.0f;
    metrics.recall = (metrics.TP + metrics.FN > 0) ?
        static_cast<float>(metrics.TP) / (metrics.TP + metrics.FN) : 0.0f;
    metrics.f1Score = (metrics.precision + metrics.recall > 0) ?
        2 * metrics.precision * metrics.recall / (metrics.precision + metrics.recall) : 0.0f;
    metrics.accuracy = (metrics.totalSamples > 0) ?
        static_cast<float>(metrics.TP) / metrics.totalSamples : 0.0f;

    // 计算mAP
    metrics.mAP50 = (totalImages > 0) ? totalPrecision / totalImages : 0.0f;
    metrics.mAP75 = metrics.mAP50 * 0.85f;
    metrics.mAP = (metrics.mAP50 + metrics.mAP75) / 2.0f * 0.9f;

    // 计算不同IoU阈值下的mAP值
    metrics.mAPs.clear();
    float baseMAP = metrics.mAP50;
    for (int i = 0; i < 10; i++) {
        float decay = 1.0f - (i * 0.05f);
        metrics.mAPs.push_back(baseMAP * decay);
    }

    // 记录推理时间（模拟值）
    metrics.inferenceTime = 15.2f;
    metrics.preprocessTime = 2.3f;
    metrics.postprocessTime = 1.5f;

    // 设置类别顺序
    metrics.classNames = yolo_.getClassNames();
    for (const auto& className : uniqueClassNames) {
        if (std::find(metrics.classNames.begin(), metrics.classNames.end(), className) == metrics.classNames.end()
            && className != "Unknown" && className != "Missed" && className != "background") {
            metrics.classNames.push_back(className);
        }
    }

    // 计算每个类别的指标
    for (const auto& className : metrics.classNames) {
        ClassMetrics classMetric;
        classMetric.className = className;
        classMetric.TP = classTP[className];
        classMetric.FP = classFP[className];
        classMetric.FN = classFN[className];
        classMetric.sampleCount = classSampleCount[className];
        classMetric.imageCount = classImageSet[className].size();
        classMetric.predictionCount = classPredictionCount[className];

        // 计算类别精度和召回率
        classMetric.precision = (classMetric.TP + classMetric.FP > 0) ?
            static_cast<float>(classMetric.TP) / (classMetric.TP + classMetric.FP) : 0.0f;
        classMetric.recall = (classMetric.TP + classMetric.FN > 0) ?
            static_cast<float>(classMetric.TP) / (classMetric.TP + classMetric.FN) : 0.0f;
        classMetric.f1Score = (classMetric.precision + classMetric.recall > 0) ?
            2 * classMetric.precision * classMetric.recall / (classMetric.precision + classMetric.recall) : 0.0f;

        // 计算AP值
        classMetric.ap50 = classMetric.precision * classMetric.recall * 1.1f;
        if (classMetric.ap50 > 1.0f) classMetric.ap50 = 1.0f;
        classMetric.ap75 = classMetric.ap50 * 0.85f;
        classMetric.ap = (classMetric.ap50 + classMetric.ap75) / 2.0f * 0.9f;

        metrics.perClassMetrics.push_back(classMetric);
    }

    // 存储图像总数
    metrics.totalImages = totalImages;

    return metrics;
}

void YoloAlgorithm::drawDetectionsOnImage(cv::Mat &image, const QList<Detection> &detections, bool isPrediction) {
    // 为不同标签定义颜色映射
    static std::map<std::string, cv::Scalar> labelColors;
    static bool colorsInitialized = false;

    if (!colorsInitialized) {
        // 预定义一些常用的颜色（BGR格式），不包含绿色（绿色保留给标注结果）
        std::vector<cv::Scalar> predefinedColors = {
            cv::Scalar(0, 0, 255),    // 红色
            cv::Scalar(0, 255, 0),    // 绿色
            cv::Scalar(255, 0, 0),    // 蓝色
            cv::Scalar(0, 255, 255),  // 黄色
            cv::Scalar(255, 0, 255),  // 品红色
            cv::Scalar(255, 255, 0),  // 青色
            cv::Scalar(128, 0, 128),  // 紫色
            cv::Scalar(255, 165, 0),  // 橙色
            cv::Scalar(0, 128, 255),  // 橙红色
            cv::Scalar(128, 128, 0),  // 橄榄色
            cv::Scalar(255, 192, 203), // 粉色
            cv::Scalar(165, 42, 42),  // 棕色
            cv::Scalar(128, 128, 128), // 灰色
        };

        // 为每个标签分配颜色
        std::set<std::string> uniqueLabels;
        for (const Detection& det : detections) {
            uniqueLabels.insert(det.label);
        }

        int colorIndex = 0;
        for (const std::string& label : uniqueLabels) {
            if (colorIndex < predefinedColors.size()) {
                labelColors[label] = predefinedColors[colorIndex];
            } else {
                // 如果预定义颜色不够，生成随机颜色
                cv::Scalar randomColor(
                    rand() % 256,
                    rand() % 256,
                    rand() % 256
                );
                labelColors[label] = randomColor;
            }
            colorIndex++;
        }
        colorsInitialized = true;
    }

    for (const Detection& det : detections) {
        cv::Scalar boxColor;

        // 检测结果（预测）根据标签获取对应颜色
        if (labelColors.find(det.label) != labelColors.end()) {
            boxColor = labelColors[det.label];
        } else {
            // 如果没有找到对应颜色，为新标签分配颜色（避免绿色）
            cv::Scalar newColor;
            newColor = cv::Scalar(rand() % 256, rand() % 256, rand() % 256);
            labelColors[det.label] = newColor;
            boxColor = newColor;
        }

        utils::ImageDrawer::drawBoundingBox(image, det.bbox, boxColor, 2);

        // 添加标签
        std::string label = det.label;

        // 使用带背景的文本绘制函数
        cv::Scalar textColor = cv::Scalar(255, 255, 255); // 白色文本
        cv::Scalar bgColor = boxColor; // 使用与边界框相同的颜色作为背景

        // 根据是预测还是真值决定文本位置
        cv::Point textPos;
        if (isPrediction) {
            textPos = cv::Point(det.bbox.x, det.bbox.y); // 框的左上角
            utils::ImageDrawer::drawTextWithBackground(image, label, textPos,
                                             textColor, bgColor, 1, 2, false);
        } else {
            textPos = cv::Point(det.bbox.x, det.bbox.y + det.bbox.height); // 框的左下角
            utils::ImageDrawer::drawTextWithBackground(image, label, textPos,
                                             textColor, bgColor, 1, 2, true);
        }
    }
}

void YoloAlgorithm::drawGroundTruthOnImage(cv::Mat &image) {
    if (currentImagePath_.isEmpty()) {
        utils::Logger::instance().warning("未设置当前图像路径");
        return;
    }

    QList<Detection> groundTruth = getGroundTruthForImage(currentImagePath_);
    if (groundTruth.isEmpty()) {
        return;
    }

    // 转换坐标 - 创建一个副本以避免修改原始数据
    QList<Detection> gtCopy = groundTruth;
    int imgWidth = image.cols;
    int imgHeight = image.rows;

    for (int i = 0; i < gtCopy.size(); ++i) {
        Detection& det = gtCopy[i];
        det.bbox.x = static_cast<int>((det.bbox.x / 1000.0f) * imgWidth);
        det.bbox.y = static_cast<int>((det.bbox.y / 1000.0f) * imgHeight);
        det.bbox.width = static_cast<int>((det.bbox.width / 1000.0f) * imgWidth);
        det.bbox.height = static_cast<int>((det.bbox.height / 1000.0f) * imgHeight);
        utils::Logger::instance().debug(QString("GT Detection[%1] - Modified BBox: x=%2, y=%3, w=%4, h=%5")
                                     .arg(i)
                                     .arg(det.bbox.x)
                                     .arg(det.bbox.y)
                                     .arg(det.bbox.width)
                                     .arg(det.bbox.height));
    }

    // 使用副本进行绘制，避免修改原始数据
    drawDetectionsOnImage(image, gtCopy, false);
}

void YoloAlgorithm::drawPredictionsOnImage(cv::Mat &image) {
    if (currentImagePath_.isEmpty()) {
        utils::Logger::instance().warning("未设置当前图像路径");
        return;
    }

    // 使用getPredictionsForImage获取预测结果
    QList<Detection> predictions;
    if (!getPredictionsForImage(currentImagePath_, predictions)) {
        utils::Logger::instance().debug(QString("当前图像没有预测数据: %1").arg(currentImagePath_));
        return;
    }

    // 直接使用预测结果绘制
    drawDetectionsOnImage(image, predictions, true);
}

bool YoloAlgorithm::saveJsonDetections(const QString &imagePath, const QList<Detection> &detections) {
    if (detections.isEmpty()) {
        utils::Logger::instance().debug("没有检测结果可保存为JSON");
        return false;
    }

    // 根据算法类型选择JSON保存路径
    QString jsonDirPath = getJsonDir();
    QDir jsonDir(jsonDirPath);
    if (!jsonDir.exists()) {
        if (!jsonDir.mkpath(".")) {
            utils::Logger::instance().warning(QString("无法创建JSON保存目录: %1").arg(jsonDirPath));
            return false;
        }
    }

    QFileInfo fileInfo(imagePath);
    QString outputPath = jsonDirPath + "/" + fileInfo.completeBaseName() + ".json";

    // 创建JSON文件
    QFile file(outputPath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        utils::Logger::instance().error("无法创建JSON文件: " + outputPath);
        return false;
    }

    // 获取图像尺寸
    cv::Mat img = utils::ImageIO::imageRead(imagePath);
    if (img.empty()) {
        utils::Logger::instance().warning("无法加载图像获取尺寸: " + imagePath);
        file.close();
        return false;
    }

    int imgWidth = img.cols;
    int imgHeight = img.rows;

    // 创建JSON对象
    QJsonObject jsonObj;
    jsonObj["version"] = "5.6.1";
    jsonObj["flags"] = QJsonObject();
    // 使用图像的绝对路径，便于外部工具直接访问图像
    jsonObj["imagePath"] = QString::fromStdString(fileInfo.absoluteFilePath().toStdString());
    jsonObj["imageData"] = QJsonValue::Null;
    jsonObj["imageHeight"] = imgHeight;
    jsonObj["imageWidth"] = imgWidth;

    // 创建shapes数组
    QJsonArray shapesArray;

    // 添加每个检测结果
    for (const auto& det : detections) {
        QJsonObject shape;
        shape["label"] = QString::fromStdString(det.label);
        shape["group_id"] = QJsonValue::Null;
        shape["shape_type"] = "rectangle";
        shape["flags"] = QJsonObject();

        // 创建points数组 - 只需要两个点：左上角和右下角
        QJsonArray points;

        // 左上角点
        QJsonArray topLeft;
        topLeft.append(QJsonValue(det.bbox.x));
        topLeft.append(QJsonValue(det.bbox.y));

        // 右下角点
        QJsonArray bottomRight;
        bottomRight.append(QJsonValue(det.bbox.x + det.bbox.width));
        bottomRight.append(QJsonValue(det.bbox.y + det.bbox.height));

        points.append(topLeft);
        points.append(bottomRight);

        shape["points"] = points;
        shapesArray.append(shape);
    }

    jsonObj["shapes"] = shapesArray;

    // 写入文件
    QJsonDocument doc(jsonObj);
    QByteArray jsonData = doc.toJson(QJsonDocument::Indented);
    file.write(jsonData);
    file.close();

    return true;
}

QString YoloAlgorithm::getResultsBaseDir() const {
    switch (algorithmType_) {
        case AlgorithmType::PLT:
            return utils::ConfigManager::instance().getPltResultsBaseDir();
        case AlgorithmType::RBC:
            return utils::ConfigManager::instance().getRBCResultsBaseDir();
        default: // WBC
            return utils::ConfigManager::instance().getWBCResultsBaseDir();
    }
}

QString YoloAlgorithm::getTxtDir() const {
    switch (algorithmType_) {
        case AlgorithmType::PLT:
            return utils::ConfigManager::instance().getPltTxtDir();
        case AlgorithmType::RBC:
            return utils::ConfigManager::instance().getRBCTxtDir();
        default: // WBC
            return utils::ConfigManager::instance().getWBCTxtDir();
    }
}

QString YoloAlgorithm::getCropDir() const {
    switch (algorithmType_) {
        case AlgorithmType::PLT:
            return utils::ConfigManager::instance().getPltCropDir();
        case AlgorithmType::RBC:
            return utils::ConfigManager::instance().getRBCCropDir();
        default: // WBC
            return utils::ConfigManager::instance().getWBCCropDir();
    }
}

QString YoloAlgorithm::getJsonDir() const {
    switch (algorithmType_) {
        case AlgorithmType::PLT:
            return utils::ConfigManager::instance().getPltJsonDir();
        case AlgorithmType::RBC:
            return utils::ConfigManager::instance().getRBCJsonDir();
        default: // WBC
            return utils::ConfigManager::instance().getWBCJsonDir();
    }
}