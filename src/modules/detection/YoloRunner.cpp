#include "modules/detection/YoloRunner.h"
#include <opencv2/dnn.hpp>
#include <opencv2/imgproc.hpp>
#include <filesystem>
#include <QElapsedTimer>
#include "utils/Logger.h"
#include <QRect>

YoloRunner::YoloRunner()
    : env_(ORT_LOGGING_LEVEL_WARNING, "YOLO") {
}

bool YoloRunner::loadClassNames(const QStringList &classes) {
    if (classes.isEmpty()) {
        utils::Logger::instance().warning("类别列表为空");
        // 使用默认的WBC类别名称
        class_names_ = {"UWBC", "CWBC", "LYM", "MON", "EOS", "BAS", "BAND", "SEG", "NSG", "RET"};
        return false;
    }

    // 清空现有类别并加载新类别
    class_names_.clear();
    for (const QString &className : classes) {
        class_names_.push_back(className.toStdString());
    }

    utils::Logger::instance().debug(QString("加载了 %1 个类别").arg(class_names_.size()));
    return true;
}

std::string YoloRunner::getClassName(int classId) const {
    if (classId >= 0 && classId < static_cast<int>(class_names_.size())) {
        return class_names_[classId];
    }
    return std::to_string(classId); // 返回类别ID的字符串形式作为后备
}

bool YoloRunner::loadModel(const std::string &model_path) {
    if (model_path.empty()) {
        utils::Logger::instance().warning("无效的模型路径：路径为空");
        return false;
    }

    if (!std::filesystem::exists(model_path)) {
        utils::Logger::instance().warning(QString("模型文件不存在：%1").arg(QString::fromStdString(model_path)));
        return false;
    }

    try {
        // 使用条件编译处理不同平台的路径差异
#ifdef _WIN32
        // Windows平台使用宽字符串
        std::wstring w_model_path(model_path.begin(), model_path.end());
        session_ = std::make_unique<Ort::Session>(env_, w_model_path.c_str(), session_options_);
#else
        // Mac/Linux平台直接使用UTF-8字符串
        std::string utf8_model_path = model_path;
        session_ = std::make_unique<Ort::Session>(env_, utf8_model_path.c_str(), session_options_);
#endif

        // 获取输入节点信息
        size_t num_input_nodes = session_->GetInputCount();
        if (num_input_nodes == 0) {
            utils::Logger::instance().warning("模型无输入节点");
            return false;
        }

        input_names_.clear();
        input_node_ptrs_.clear();
        for (size_t i = 0; i < num_input_nodes; i++) {
            Ort::AllocatorWithDefaultOptions allocator;
            input_names_.push_back(session_->GetInputNameAllocated(i, allocator).get());
            input_node_ptrs_.push_back(input_names_.back().c_str());
            utils::Logger::instance().debug(QString("input_names：%1").arg(input_names_.back().c_str()));
        }

        // 获取输出节点信息
        size_t num_output_nodes = session_->GetOutputCount();
        if (num_output_nodes == 0) {
            utils::Logger::instance().warning("模型无输出节点");
            return false;
        }

        output_names_.clear();
        output_node_ptrs_.clear();
        for (size_t i = 0; i < num_output_nodes; i++) {
            Ort::AllocatorWithDefaultOptions allocator;
            output_names_.push_back(session_->GetOutputNameAllocated(i, allocator).get());
            output_node_ptrs_.push_back(output_names_.back().c_str());
            utils::Logger::instance().debug(QString("output_names：%1").arg(output_names_.back().c_str()));
        }

        // 获取并验证模型输入尺寸
        Ort::TypeInfo type_info = session_->GetInputTypeInfo(0);
        auto tensor_info = type_info.GetTensorTypeAndShapeInfo();
        auto input_shape = tensor_info.GetShape();

        if (input_shape.size() < 4) {
            utils::Logger::instance().warning(QString("无效的输入维度：%1").arg(input_shape.size()));
            return false;
        }

        model_input_height_ = static_cast<int>(input_shape[2]);
        model_input_width_ = static_cast<int>(input_shape[3]);

        if (model_input_height_ <= 0 || model_input_width_ <= 0) {
            utils::Logger::instance().warning(QString("无效的输入尺寸：%1x%2")
                .arg(model_input_width_)
                .arg(model_input_height_));
            return false;
        }

        utils::Logger::instance().debug(QString("- 输入尺寸：%1x%2")
            .arg(model_input_width_)
            .arg(model_input_height_));
        utils::Logger::instance().debug(QString("- 输入节点数：%1").arg(input_names_.size()));
        utils::Logger::instance().debug(QString("- 输出节点数：%1").arg(output_names_.size()));
        utils::Logger::instance().debug(QString("- 置信度阈值：%1").arg(conf_threshold_));
        utils::Logger::instance().debug(QString("- NMS阈值：%1").arg(nms_threshold_));
        return true;

    } catch (const Ort::Exception &e) {
        utils::Logger::instance().error(QString("模型加载失败：%1").arg(e.what()));
        return false;
    } catch (const std::exception &e) {
        utils::Logger::instance().error(QString("意外错误：%1").arg(e.what()));
        return false;
    }
}

bool YoloRunner::processImage(const cv::Mat &input,
                           std::vector<cv::Rect> &boxes,
                           std::vector<std::string> &labels,
                           std::vector<float> &scores) {
    // 基本检查
    if (!session_) {
        utils::Logger::instance().error("模型未加载，请先加载模型");
        return false;
    }

    if (input_names_.empty() || output_names_.empty()) {
        utils::Logger::instance().error("模型输入输出节点未正确初始化");
        return false;
    }

    if (input.empty()) {
        utils::Logger::instance().error("输入图像为空");
        return false;
    }

    if (input.type() != CV_8UC3) {
        utils::Logger::instance().error("输入图像格式错误，需要3通道8位图像");
        return false;
    }

    boxes.clear();
    labels.clear();
    scores.clear();

    // 使用从模型获取的输入尺寸
    cv::Mat input_resized;
    LetterBoxInfo letterbox_info = letterBoxWithInfo(input, model_input_width_, model_input_height_, input_resized);

    if (input_resized.empty()) {
        utils::Logger::instance().error("图像缩放失败");
        return false;
    }

    // 转换为浮点型并归一化
    input_resized.convertTo(input_resized, CV_32F, 1.0 / 255.0);
    cv::cvtColor(input_resized, input_resized, cv::COLOR_BGR2RGB);

    // 准备输入张量
    std::vector<float> input_tensor_values(3 * model_input_height_ * model_input_width_);
    std::vector<cv::Mat> chw(3);
    for (int i = 0; i < 3; ++i) {
        chw[i] = cv::Mat(model_input_height_, model_input_width_, CV_32F, input_tensor_values.data() + i * model_input_height_ * model_input_width_);
    }
    cv::split(input_resized, chw);

    // 检查内存分配
    if (!input_tensor_values.data()) {
        utils::Logger::instance().error("输入张量内存分配失败");
        return false;
    }

    // 输入形状
    std::vector<int64_t> input_shape = {1, 3, model_input_height_, model_input_width_};

    try {
        Ort::MemoryInfo memory_info = Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);
        Ort::Value input_tensor = Ort::Value::CreateTensor<float>(
            memory_info, input_tensor_values.data(), input_tensor_values.size(),
            input_shape.data(), input_shape.size());

        if (!input_tensor.IsTensor()) {
            utils::Logger::instance().error("创建输入张量失败");
            return false;
        }

        // 使用缓存的节点名称指针运行推理
        std::vector<Ort::Value> output_tensors = session_->Run(
            Ort::RunOptions{nullptr},
            input_node_ptrs_.data(), &input_tensor, 1,
            output_node_ptrs_.data(), output_node_ptrs_.size());

        if (output_tensors.empty()) {
            utils::Logger::instance().error("模型推理未返回输出");
            return false;
        }

        float* output = output_tensors.front().GetTensorMutableData<float>();
        if (!output) {
            utils::Logger::instance().error("无法访问输出张量数据");
            return false;
        }

        auto output_shape = output_tensors.front().GetTensorTypeAndShapeInfo().GetShape();
        if (output_shape.size() < 2) {
            utils::Logger::instance().error("输出张量维度不正确");
            return false;
        }

        int num_channels = static_cast<int>(output_shape[1]);
        int num_preds = static_cast<int>(output_shape[2]);

        if (num_channels < 5) { // 至少需要 4个坐标值 + 1个类别
            utils::Logger::instance().error("输出通道数不足");
            return false;
        }

        std::vector<cv::Rect> raw_boxes;
        std::vector<float> confidences;
        std::vector<int> class_ids;

        // 处理预测结果
        for (size_t i = 0; i < num_preds; ++i) {
            float conf = 0;
            int class_id = -1;
            for (int j = 4; j < num_channels; ++j) {
                float curr_conf = output[j * num_preds + i];
                if (conf < curr_conf) {
                    conf = curr_conf;
                    class_id = static_cast<int>(j - 4);
                }
            }
            if (conf < conf_threshold_) continue;

            float x = output[0 * num_preds + i];
            float y = output[1 * num_preds + i];
            float w = output[2 * num_preds + i];
            float h = output[3 * num_preds + i];

            // 检查原始值的有效性
            if (w <= 0 || h <= 0) {
                utils::Logger::instance().debug(QString("检测到无效的边界框尺寸: [w=%1, h=%2]").arg(w).arg(h));
                continue; // 跳过无效的边界框
            }

            // 自动检测坐标格式：如果坐标值小于1，则认为是归一化坐标
            bool isNormalized = (x < 1.0f && y < 1.0f && w < 1.0f && h < 1.0f);
            int left, top, width, height;

            if (isNormalized) {
                // 归一化坐标处理（YOLOv8等）
                float normalized_left = x - w * 0.5f; // 归一化的左上角x坐标
                float normalized_top = y - h * 0.5f;  // 归一化的左上角y坐标

                // 转换为像素坐标（相对于模型输入尺寸）
                left = static_cast<int>(normalized_left * model_input_width_);
                top = static_cast<int>(normalized_top * model_input_height_);
                width = static_cast<int>(w * model_input_width_);
                height = static_cast<int>(h * model_input_height_);
            } else {
                // 直接像素坐标处理（YOLOv11等）
                left = static_cast<int>(x - w * 0.5f); // 左上角x坐标
                top = static_cast<int>(y - h * 0.5f);  // 左上角y坐标
                width = static_cast<int>(w);
                height = static_cast<int>(h);
            }

            // 确保边界框在有效范围内
            left = std::max(0, left);
            top = std::max(0, top);
            width = std::max(1, width);
            height = std::max(1, height);

            raw_boxes.emplace_back(left, top, width, height);
            confidences.push_back(conf);
            class_ids.push_back(class_id);
        }

        if (!raw_boxes.empty()) {
            // 应用NMS
            std::vector<int> indices;
            cv::dnn::NMSBoxes(raw_boxes, confidences, conf_threshold_, nms_threshold_, indices);

            // 处理NMS后的结果
            for (int idx : indices) {
                if (idx >= 0 && idx < static_cast<int>(raw_boxes.size())) {
                    boxes.push_back(restoreCoords(raw_boxes[idx], letterbox_info));
                    scores.push_back(confidences[idx]);
                    int class_id = class_ids[idx];
                    if (class_id >= 0 && class_id < static_cast<int>(class_names_.size())) {
                        labels.push_back(class_names_[class_id]);
                    } else {
                        labels.push_back(std::to_string(class_id));
                        utils::Logger::instance().warning(QString("未知的类别ID: %1").arg(class_id));
                    }
                }
            }
        }

        // 记录所有经过NMS筛选后的检测结果
        utils::Logger::instance().debug(QString("NMS筛选后共检测到 %1 个目标").arg(boxes.size()));
        for (int i = 0; i < static_cast<int>(boxes.size()); i++) {
            const auto& box = boxes[i];
            const auto& score = scores[i];
            const auto& label = labels[i];
            utils::Logger::instance().debug(QString("检测结果 #%1: 类别=%2, 边界框=[x:%3, y:%4, w:%5, h:%6], 置信度=%7")
                .arg(i + 1)  // 从1开始编号更直观
                .arg(QString::fromStdString(label))
                .arg(box.x)      // 整数坐标
                .arg(box.y)
                .arg(box.width)
                .arg(box.height)
                .arg(score, 0, 'f', 3)); // 置信度保留3位小数
        }

        return true;
    } catch (const Ort::Exception& e) {
        utils::Logger::instance().error(QString("ONNX Runtime 错误: %1").arg(e.what()));
        return false;
    } catch (const std::exception& e) {
        utils::Logger::instance().error(QString("推理过程发生错误: %1").arg(e.what()));
        return false;
    } catch (...) {
        utils::Logger::instance().error("推理过程发生未知错误");
        return false;
    }
}

bool YoloRunner::infer(const cv::Mat &input,
                       std::vector<cv::Rect> &boxes,
                       std::vector<std::string> &labels,
                       std::vector<float> &scores) {
    return processImage(input, boxes, labels, scores);
}

// Letterbox相关方法实现
LetterBoxInfo YoloRunner::letterBoxWithInfo(const cv::Mat& input, int target_width, int target_height, cv::Mat& output) const {
    // 计算缩放比例
    float scale = std::min(
        static_cast<float>(target_width) / static_cast<float>(input.cols),
        static_cast<float>(target_height) / static_cast<float>(input.rows)
    );

    int new_w = static_cast<int>(input.cols * scale);
    int new_h = static_cast<int>(input.rows * scale);

    // 缩放图像
    cv::resize(input, output, cv::Size(new_w, new_h));

    // 计算填充
    int pad_left = (target_width - new_w) / 2;
    int pad_right = target_width - new_w - pad_left;
    int pad_top = (target_height - new_h) / 2;
    int pad_bottom = target_height - new_h - pad_top;

    // 应用填充
    cv::copyMakeBorder(output, output, pad_top, pad_bottom, pad_left, pad_right,
                       cv::BORDER_CONSTANT, cv::Scalar(0, 0, 0));

    return {scale, pad_left, pad_top};
}

cv::Rect YoloRunner::restoreCoords(const cv::Rect& box, const LetterBoxInfo& info) const {
    // 减去填充
    float x1 = static_cast<float>(box.x) - static_cast<float>(info.pad_left);
    float y1 = static_cast<float>(box.y) - static_cast<float>(info.pad_top);
    float x2 = static_cast<float>(box.x + box.width) - static_cast<float>(info.pad_left);
    float y2 = static_cast<float>(box.y + box.height) - static_cast<float>(info.pad_top);

    // 除以缩放比例
    x1 /= info.scale;
    y1 /= info.scale;
    x2 /= info.scale;
    y2 /= info.scale;

    // 转换为rect
    int orig_w = static_cast<int>(std::round(x2 - x1));
    int orig_h = static_cast<int>(std::round(y2 - y1));

    return cv::Rect(
        std::max(0, static_cast<int>(std::round(x1))),
        std::max(0, static_cast<int>(std::round(y1))),
        std::max(1, orig_w),  // 确保宽度至少为1
        std::max(1, orig_h)   // 确保高度至少为1
    );
}

