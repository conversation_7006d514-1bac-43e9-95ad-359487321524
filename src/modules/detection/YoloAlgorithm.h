#ifndef YOLO_ALGORITHM_H
#define YOLO_ALGORITHM_H

#include "modules/detection/YoloRunner.h"
#include "modules/detection/DetectionTypes.h"
#include "utils/ExcelExporter.h"
#include "utils/ImageDrawer.h"
#include "utils/Logger.h"
#include "utils/ImageIO.h"

#include <QDebug>
#include <QMap>
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QString>
#include <QStringList>
#include <QColor>
#include <opencv2/core/core.hpp>

enum class AlgorithmType {
    WBC,
    RBC,
    PLT
};


class YoloAlgorithm {
public:
    // 构造函数
    YoloAlgorithm(AlgorithmType type = AlgorithmType::WBC);

    // 基本信息
    bool isModelLoaded() const { return modelLoaded_; }

    // 模型管理
    bool loadModel(const std::string &modelPath);
    bool loadClassNames(const QStringList &classes);
    const std::vector<std::string>& getClassNames() const { return yolo_.getClassNames(); }

    // 算法类型设置
    void setAlgorithmType(AlgorithmType type) { algorithmType_ = type; }
    AlgorithmType getAlgorithmType() const { return algorithmType_; }

    // 参数设置
    void setConfidenceThreshold(float value) {
        if (value <= 0.0f || value > 1.0f) {
            utils::Logger::instance().warning(QString("置信度阈值无效: %1，必须在0到1之间").arg(value));
            return;
        }
        confidenceThreshold_ = value;
        yolo_.setConfThreshold(value);
    }

    void setNmsThreshold(float value) {
        if (value <= 0.0f || value > 1.0f) {
            utils::Logger::instance().warning(QString("NMS阈值无效: %1，必须在0到1之间").arg(value));
            return;
        }
        nmsThreshold_ = value;
        yolo_.setNmsThreshold(value);
    }

    void setSaveResults(bool save) { saveResults_ = save; }
    void setSaveTxtResults(bool save) { saveTxtResults_ = save; }
    void setSaveJsonResults(bool save) { saveJsonResults_ = save; }

    // 固定宽高裁切设置
    void setUseFixedSize(bool use) { useFixedSize_ = use; }
    void setFixedSize(int width, int height) {
        fixedWidth_ = width;
        fixedHeight_ = height;
    }

    // 边界目标过滤设置
    void setFilterBoundaryTargets(bool filter) { filterBoundaryTargets_ = filter; }
    bool getFilterBoundaryTargets() const { return filterBoundaryTargets_; }
    void setBoundaryThreshold(int threshold) { boundaryThreshold_ = threshold; }
    int getBoundaryThreshold() const { return boundaryThreshold_; }

    // 图像路径管理
    void setCurrentImagePath(const QString& path);

    // 预测功能
    bool predict(const cv::Mat &input);
    bool batchPredict(const QStringList &imagePaths);
    void clearPredictions();
    bool getPredictionsForImage(const QString &imagePath, QList<Detection> &detections);
    const QMap<QString, QList<Detection>>& getAllPredictions() const;
    bool exportBatchPredictions();

    // 标注数据管理
    bool loadGroundTruthBatch(const QStringList &gtPaths);
    void clearGroundTruths();
    QList<Detection> getGroundTruthForImage(const QString &imagePath);
    const QMap<QString, QList<Detection>>& getAllGroundTruth() const;

    // 新增的标注格式加载方法
    bool loadLabelmeGroundTruth(const QString &jsonPath);
    bool loadYoloGroundTruth(const QString &gtPath);

    // 结果保存
    bool saveTxtDetections(const QString &imagePath, const QList<Detection> &detections);
    bool saveJsonDetections(const QString &imagePath, const QList<Detection> &detections);
    bool saveCroppedObjects(const QString &imagePath, const cv::Mat &image, const QList<Detection> &detections);

    // 验证功能
    EvaluationMetrics validatePredictions();
    float calculateIoU(const cv::Rect& box1, const cv::Rect& box2);

    // 可视化
    void drawDetectionsOnImage(cv::Mat &image, const QList<Detection> &detections, bool isPrediction);
    void drawGroundTruthOnImage(cv::Mat &image);
    void drawPredictionsOnImage(cv::Mat &image);

    // 进度回调
    using ProgressCallback = std::function<void(int, int)>;
    void setProgressCallback(ProgressCallback callback) { progressCallback_ = callback; }

private:
    // YOLO引擎
    YoloRunner yolo_;

    // 算法类型
    AlgorithmType algorithmType_;

    // 路径
    QString currentImagePath_;

    // 参数
    float confidenceThreshold_;
    float nmsThreshold_;
    bool modelLoaded_;
    bool saveResults_;
    bool saveTxtResults_;
    bool saveJsonResults_;
    bool useFixedSize_;
    int fixedWidth_;
    int fixedHeight_;
    bool filterBoundaryTargets_;  // 新增：是否过滤边界目标
    int boundaryThreshold_;       // 新增：边界阈值（像素）
    int lastExcludedCount_ = 0; // 用于记录最近一次预测中剔除的边界目标数量

    // 数据存储
    QMap<QString, QList<Detection>> predictedResults_;
    QMap<QString, QList<Detection>> groundTruthMap_;
    std::vector<std::tuple<std::string, Detection>> batchDetectionsForExport_;
    std::vector<std::string> batchProcessedFiles_; // 记录所有处理过的文件

    // 进度回调
    ProgressCallback progressCallback_;

    // 私有辅助函数
    QString getResultsBaseDir() const;
    QString getTxtDir() const;
    QString getCropDir() const;
    QString getJsonDir() const;
};

#endif // YOLO_ALGORITHM_H