#ifndef YOLORUNNER_H
#define YOLORUNNER_H

#include <onnxruntime_cxx_api.h>
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>
#include <memory>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QString>
#include "DetectionTypes.h"

class YoloRunner {
public:
    <PERSON><PERSON><PERSON><PERSON><PERSON>();
    ~YoloRunner() = default;

    bool loadModel(const std::string &modelPath);
    bool loadClassNames(const QStringList &classes);
    std::string getClassName(int classId) const;
    const std::vector<std::string>& getClassNames() const { return class_names_; }
    bool infer(const cv::Mat &input,
               std::vector<cv::Rect> &boxes,
               std::vector<std::string> &labels,
               std::vector<float> &scores);

    // 获取模型输入尺寸
    int getInputWidth() const { return model_input_width_; }
    int getInputHeight() const { return model_input_height_; }

    // 添加阈值设置方法
    void setConfThreshold(float value) { conf_threshold_ = value; }
    void setNmsThreshold(float value) { nms_threshold_ = value; }
    float getConfThreshold() const { return conf_threshold_; }
    float getNmsThreshold() const { return nms_threshold_; }

private:
    // 处理单张图像的推理，供 infer 和 batchInfer 方法调用
    bool processImage(const cv::Mat &input,
                     std::vector<cv::Rect> &boxes,
                     std::vector<std::string> &labels,
                     std::vector<float> &scores);

    // Letterbox相关方法
    LetterBoxInfo letterBoxWithInfo(const cv::Mat& input, int target_width, int target_height, cv::Mat& output) const;
    cv::Rect restoreCoords(const cv::Rect& box, const LetterBoxInfo& info) const;

    Ort::Env env_;
    std::unique_ptr<Ort::Session> session_;
    Ort::SessionOptions session_options_;

    // 存储实际的字符串内容
    std::vector<std::string> input_names_;
    std::vector<std::string> output_names_;
    std::vector<std::string> class_names_;

    // 缓存指向字符串内容的指针，在调用 Run 时使用
    std::vector<const char*> input_node_ptrs_;
    std::vector<const char*> output_node_ptrs_;

    // 模型输入尺寸
    int model_input_width_{640};
    int model_input_height_{640};

    // 阈值参数
    float conf_threshold_{0.25f};
    float nms_threshold_{0.5f};
};

#endif // YOLORUNNER_H