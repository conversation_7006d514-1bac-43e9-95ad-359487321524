#include "HgbTypes.h"
#include <sstream>
#include <iomanip>
#include <cmath>

double HgbParseResult::calculateHgbConcentration() const {
    if (hgbData.measurementValues.empty()) {
        return 0.0;
    }
    
    // 简化的HGB浓度计算公式
    double rawValue = hgbData.mean;
    double blankValue = 0.0;
    
    // 如果有空白值，计算空白平均值
    if (!hgbData.blankValues.empty()) {
        double blankSum = 0.0;
        for (uint16_t value : hgbData.blankValues) {
            blankSum += value;
        }
        blankValue = blankSum / hgbData.blankValues.size();
    }
    
    // 应用校准系数计算HGB浓度 (g/dL)
    double concentration = (rawValue - blankValue) * calibrationCoef.fHGB;
    
    // 确保浓度在合理范围内 (0-25 g/dL)
    if (concentration < 0.0) concentration = 0.0;
    if (concentration > 25.0) concentration = 25.0;
    
    return concentration;
}

std::string HgbParseResult::getStatisticsSummary() const {
    std::ostringstream oss;
    
    oss << "=== HGB数据统计摘要 ===\n";
    oss << "解析结果: " << (result == HgbResult::SUCCESS ? "成功" : "失败") << "\n";
    
    if (result != HgbResult::SUCCESS) {
        oss << "错误信息: " << errorMessage << "\n";
        return oss.str();
    }
    
    // 基本信息
    oss << "仪器类型: " << headerInfo.machineType << "\n";
    oss << "版本信息: " << headerInfo.version << "\n";
    oss << "仪器SN: " << headerInfo.instrumentSN << "\n";
    
    // 测量数据统计
    oss << "\n--- 测量数据 ---\n";
    oss << "测量点数: " << hgbData.measurementCount << "\n";
    if (hgbData.measurementCount > 0) {
        oss << std::fixed << std::setprecision(2);
        oss << "平均值: " << hgbData.mean << "\n";
        oss << "标准差: " << hgbData.stdDev << "\n";
        oss << "最小值: " << hgbData.minValue << "\n";
        oss << "最大值: " << hgbData.maxValue << "\n";
        oss << "变异系数: " << (hgbData.mean > 0 ? (hgbData.stdDev / hgbData.mean * 100) : 0) << "%\n";
    }
    
    // 空白数据统计
    oss << "\n--- 空白数据 ---\n";
    oss << "空白点数: " << hgbData.blankCount << "\n";
    if (!hgbData.blankValues.empty()) {
        double blankSum = 0.0;
        uint16_t blankMin = hgbData.blankValues[0];
        uint16_t blankMax = hgbData.blankValues[0];
        
        for (uint16_t value : hgbData.blankValues) {
            blankSum += value;
            if (value < blankMin) blankMin = value;
            if (value > blankMax) blankMax = value;
        }
        
        double blankMean = blankSum / hgbData.blankValues.size();
        oss << std::fixed << std::setprecision(2);
        oss << "空白平均值: " << blankMean << "\n";
        oss << "空白最小值: " << blankMin << "\n";
        oss << "空白最大值: " << blankMax << "\n";
    }
    
    // HGB浓度
    oss << "\n--- HGB浓度 ---\n";
    oss << std::fixed << std::setprecision(3);
    oss << "HGB浓度: " << calculateHgbConcentration() << " g/dL\n";
    
    // 校准系数
    oss << "\n--- 校准系数 ---\n";
    oss << std::fixed << std::setprecision(4);
    oss << "HGB系数: " << calibrationCoef.fHGB << "\n";
    
    return oss.str();
}