#ifndef HGB_PARSER_H
#define HGB_PARSER_H

#include "HgbTypes.h"
#include <QString>
#include <QByteArray>
#include <memory>

class HgbParser {
public:
    HgbParser();
    ~HgbParser();

    // 解析INF.zip文件
    HgbParseResult parseInfZipFile(const QString& zipFilePath);
    
    // 解析INF文件
    HgbParseResult parseInfFile(const QString& infFilePath);
    
    // 解析INF缓冲区数据
    HgbParseResult parseInfBuffer(const uint8_t* buffer, int bufferSize);

private:
    // ZIP文件解压
    bool unzipFile(const QString& zipFilePath, QByteArray& unzippedData);
    bool unzipZipFile(const QString& zipFilePath, QByteArray& unzippedData);
    bool isGzipFile(const QString& filePath);
    bool unzipGzipFile(const QString& gzipFilePath, QByteArray& unzippedData);
    
    // 读取INF缓冲区
    HgbResult readInfBuffer(const uint8_t* buffer, int bufferSize, HgbParseResult& result);
    
    // 读取头部信息 (添加 isBigEndian 参数)
    HgbResult readHeaderInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian);
    
    // 读取版本信息
    HgbResult readVersionInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian);
    
    // 读取样本信息
    HgbResult readSampleInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian);
    
    // 读取测量信息
    HgbResult readMeasureInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian);
    
    // 读取HGB脉冲数据
    HgbResult readHgbPulse(const uint8_t* pStart, const uint8_t* pEnd,
                          std::vector<uint16_t>& pulseData, uint32_t& dataLength, bool isBigEndian);
    
    // 读取校准系数
    HgbResult readCalibrationCoef(const QString& fileName, CalibrationCoef& coef);
    
    // CRC校验
    uint32_t calculateCrcCcitt(const uint8_t* data, uint32_t length);
    
    // 数据统计计算 (注意这里使用的是 HgbData)
    void calculateStatistics(HgbData& measurement);
    
    // 边界检查
    bool checkBoundary(const uint8_t* ptr, const uint8_t* end, size_t size = 1);

private:
    ChannelInfo channelInfo_;
    static const int TYPE_LENGTHS[7];  // 数据类型长度映射
};

#endif // HGB_PARSER_H