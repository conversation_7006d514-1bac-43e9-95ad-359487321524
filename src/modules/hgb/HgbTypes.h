#ifndef HGB_TYPES_H
#define HGB_TYPES_H

#include <vector>
#include <string>
#include <cstdint>

// HGB数据解析结果枚举
enum class HgbResult {
    SUCCESS = 0,        // 数据完整
    OPEN_FAILED,        // 文件打开失败
    CLOSE_FAILED,       // 文件关闭失败
    DATA_ERROR,         // 数据错误
    NO_MEMORY,          // 内存不足
    INVALID_FORMAT      // 无效格式
};

// 校准系数结构体
struct CalibrationCoef {
    float fHGB = 1.0f;
    float fMCV = 1.0f;
    float fPLT = 1.0f;
    float fRBC = 1.0f;
    float fWBC = 1.0f;
};

// 通道信息结构体
struct ChannelInfo {
    bool bSelectBaso = true;
    bool bSelectDiff = true;
    bool bSelectNrbc = true;
    bool bSelectRet = true;
    bool bSelectCBC = true;
};

// HGB数据结构 (统一命名为 HgbData)
struct HgbData {
    std::vector<uint16_t> measurementValues;  // 测量值
    std::vector<uint16_t> blankValues;        // 空白值
    uint32_t measurementCount = 0;            // 测量点数
    uint32_t blankCount = 0;                  // 空白点数
    
    // 统计信息
    double mean = 0.0;                        // 平均值
    double stdDev = 0.0;                      // 标准差
    uint16_t minValue = 0;                    // 最小值
    uint16_t maxValue = 0;                    // 最大值
};

// INF文件头信息
struct InfHeaderInfo {
    int32_t machineType = 0;                  // 仪器类型
    std::string version;                      // 版本信息
    std::string sampleInfo;                   // 样本信息
    std::string measureInfo;                  // 测量信息
    std::string instrumentSN;                 // 仪器序列号 (之前缺失的字段)
};

// HGB解析结果结构体
struct HgbParseResult {
    HgbResult result = HgbResult::SUCCESS;
    InfHeaderInfo headerInfo;
    HgbData hgbData;                          // 使用 HgbData 类型
    CalibrationCoef calibrationCoef;
    std::string errorMessage;
    
    // 计算HGB浓度
    double calculateHgbConcentration() const;
    
    // 获取统计摘要
    std::string getStatisticsSummary() const;
};

#endif // HGB_TYPES_H