#include "HgbParser.h"
#include "utils/Logger.h"
#include <QFile>
#include <QDir>
#include <QProcess>
#include <QTemporaryDir>
#include <QStandardPaths>
#include <cstring>
#include <algorithm>
#include <cmath>
#include <sstream>
#include <iomanip>
#include <QElapsedTimer>

// 字节序转换辅助函数
namespace {
    uint16_t readUint16(const uint8_t* ptr, bool isBigEndian) {
        uint16_t value = *reinterpret_cast<const uint16_t*>(ptr);
        return isBigEndian ? ((value & 0xFF) << 8) | ((value >> 8) & 0xFF) : value;
    }

    uint32_t readUint32(const uint8_t* ptr, bool isBigEndian) {
        uint32_t value = *reinterpret_cast<const uint32_t*>(ptr);
        return isBigEndian ? ((value & 0xFF) << 24) | (((value >> 8) & 0xFF) << 16) | (((value >> 16) & 0xFF) << 8) | ((value >> 24) & 0xFF) : value;
    }

    int16_t readInt16(const uint8_t* ptr, bool isBigEndian) {
        return static_cast<int16_t>(readUint16(ptr, isBigEndian));
    }

    int32_t readInt32(const uint8_t* ptr, bool isBigEndian) {
        return static_cast<int32_t>(readUint32(ptr, isBigEndian));
    }
}

const int HgbParser::TYPE_LENGTHS[7] = {0, 1, 2, 2, 4, 4, 8};

HgbParser::HgbParser() {
    channelInfo_.bSelectBaso = true;
    channelInfo_.bSelectDiff = true;
    channelInfo_.bSelectNrbc = true;
    channelInfo_.bSelectRet = true;
    channelInfo_.bSelectCBC = true;
}

HgbParser::~HgbParser() {
}

HgbParseResult HgbParser::parseInfZipFile(const QString& zipFilePath) {
    if (zipFilePath.isEmpty()) return {HgbResult::OPEN_FAILED, {}, {}, {}, "ZIP文件路径为空"};
    if (!QFile::exists(zipFilePath)) return {HgbResult::OPEN_FAILED, {}, {}, {}, "ZIP文件不存在"};
    
    utils::Logger::instance().info("准备解压文件: " + zipFilePath);

    QByteArray unzippedData;
    if (!unzipFile(zipFilePath, unzippedData)) {
        return {HgbResult::OPEN_FAILED, {}, {}, {}, "ZIP文件解压失败"};
    }
    
    utils::Logger::instance().info("解压完成，数据大小: " + QString::number(unzippedData.size()));
    return parseInfBuffer(reinterpret_cast<const uint8_t*>(unzippedData.data()), unzippedData.size());
}

HgbParseResult HgbParser::parseInfFile(const QString& infFilePath) {
    QFile file(infFilePath);
    if (!file.open(QIODevice::ReadOnly)) return {HgbResult::OPEN_FAILED, {}, {}, {}, "无法打开INF文件"};
    QByteArray fileData = file.readAll();
    utils::Logger::instance().info("读取INF文件成功，大小: " + QString::number(fileData.size()));
    return parseInfBuffer(reinterpret_cast<const uint8_t*>(fileData.data()), fileData.size());
}

HgbParseResult HgbParser::parseInfBuffer(const uint8_t* buffer, int bufferSize) {
    HgbParseResult result;
    if (!buffer || bufferSize <= 0) return {HgbResult::DATA_ERROR, {}, {}, {}, "无效缓冲区"};
    
    result.result = readInfBuffer(buffer, bufferSize, result);
    
    if (result.result == HgbResult::SUCCESS) {
        calculateStatistics(result.hgbData);
        utils::Logger::instance().info("HGB数据解析成功，测量点数: " + QString::number(result.hgbData.measurementCount));
    } else {
        utils::Logger::instance().error("HGB数据解析失败: " + QString::fromStdString(result.errorMessage));
    }
    return result;
}

bool HgbParser::unzipFile(const QString& zipFilePath, QByteArray& unzippedData) {
    return isGzipFile(zipFilePath) ? unzipGzipFile(zipFilePath, unzippedData) : unzipZipFile(zipFilePath, unzippedData);
}

bool HgbParser::isGzipFile(const QString& filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) return false;
    QByteArray header = file.read(2);
    return (header.size() >= 2 && (unsigned char)header[0] == 0x1F && (unsigned char)header[1] == 0x8B);
}

bool HgbParser::unzipGzipFile(const QString& gzipFilePath, QByteArray& unzippedData) {
    QProcess gunzip;
    gunzip.start("gunzip", QStringList() << "-c" << gzipFilePath);
    if (!gunzip.waitForStarted()) return false;
    while (!gunzip.waitForFinished(200)) unzippedData += gunzip.readAllStandardOutput();
    unzippedData += gunzip.readAllStandardOutput();
    return gunzip.exitCode() == 0 && !unzippedData.isEmpty();
}

bool HgbParser::unzipZipFile(const QString& zipFilePath, QByteArray& unzippedData) {
    QTemporaryDir tempDir;
    if (!tempDir.isValid()) return false;

    auto runUnzip = [&](bool usePwd) -> bool {
        QProcess unzip;
        unzip.setStandardInputFile(QProcess::nullDevice());
        QStringList args;
        args << "-o" << (usePwd ? "-P" : "") << (usePwd ? "password" : "") << zipFilePath << "-d" << tempDir.path();
        args.removeAll("");
        unzip.start("unzip", args);
        if (!unzip.waitForStarted()) return false;
        QElapsedTimer timer; timer.start();
        while (!unzip.waitForFinished(100)) {
            unzip.readAllStandardOutput(); unzip.readAllStandardError();
            if (timer.elapsed() > 15000) { unzip.kill(); return false; }
        }
        return unzip.exitCode() == 0;
    };

    if (!runUnzip(true) && !runUnzip(false)) return false;

    QDir dir(tempDir.path());
    QStringList infFiles = dir.entryList(QStringList() << "*.inf" << "*.INF", QDir::Files);
    if (infFiles.isEmpty()) return false;

    QFile file(dir.absoluteFilePath(infFiles.first()));
    if (!file.open(QIODevice::ReadOnly)) return false;
    unzippedData = file.readAll();
    return !unzippedData.isEmpty();
}

HgbResult HgbParser::readInfBuffer(const uint8_t* buffer, int bufferSize, HgbParseResult& result) {
    const uint8_t* pRecord = buffer;
    const uint8_t* pEnd = buffer + bufferSize;

    if (bufferSize < 21) { result.errorMessage = "Header too small"; return HgbResult::DATA_ERROR; }
    
    bool isBigEndian = (buffer[18] != 0);
    int nRecordTotal = readInt16(buffer + 19, isBigEndian);
    
    for (int i = 0; i < nRecordTotal; i++) {
        if (pRecord + 10 > pEnd) break;
        uint32_t len = readUint32(pRecord + 4, isBigEndian);
        int16_t type = readInt16(pRecord + 8, isBigEndian);
        
        if (pRecord + 4 + len > pEnd) { result.errorMessage = "Record overflow"; return HgbResult::DATA_ERROR; }

        const uint8_t* pData = pRecord; 
        
        switch(type) {
            case 0: readHeaderInfo(pData, pEnd, result.headerInfo, isBigEndian); break;
            case 1: readVersionInfo(pData, pEnd, result.headerInfo, isBigEndian); break;
            case 2: readSampleInfo(pData, pEnd, result.headerInfo, isBigEndian); break;
            case 3: readMeasureInfo(pData, pEnd, result.headerInfo, isBigEndian); break;
            case 40: {
                if (checkBoundary(pData, pEnd, 20)) {
                    int subType = readUint16(pData + 18, isBigEndian);
                    if (subType == 7 && channelInfo_.bSelectCBC) {
                        readHgbPulse(pData, pEnd, result.hgbData.measurementValues, result.hgbData.measurementCount, isBigEndian);
                    } else if (subType == 17 && channelInfo_.bSelectCBC) {
                        readHgbPulse(pData, pEnd, result.hgbData.blankValues, result.hgbData.blankCount, isBigEndian);
                    }
                }
                break;
            }
        }
        
        pRecord += 4 + len; 
    }
    return HgbResult::SUCCESS;
}

// ... 辅助函数 ...
HgbResult HgbParser::readHeaderInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    if (pStart + 25 > pEnd) return HgbResult::DATA_ERROR;
    headerInfo.machineType = readInt32(pStart + 21, isBigEndian);
    return HgbResult::SUCCESS;
}

void readStringParams(const uint8_t* pStart, const uint8_t* pEnd, bool isBigEndian, std::function<void(uint32_t, QString)> callback) {
    const uint8_t* pTemp = pStart + 16;
    if (pTemp + 4 > pEnd) return;
    uint32_t count = readUint32(pTemp, isBigEndian);
    // 智能偏移修正
    if (count > 500) { pTemp += 1; if (pTemp + 4 <= pEnd) count = readUint32(pTemp, isBigEndian); }
    pTemp += 4;
    
    for(uint32_t i=0; i<count && pTemp < pEnd; i++) {
        if (pTemp + 10 > pEnd) break;
        uint32_t id = readUint32(pTemp, isBigEndian); pTemp += 4;
        pTemp++; 
        uint32_t len = readUint32(pTemp, isBigEndian); pTemp += 4;
        if (pTemp + len > pEnd) break;
        if (len > 0 && len < 256) {
            char buf[256] = {0}; memcpy(buf, pTemp, len);
            callback(id, QString::fromLatin1(buf));
        }
        pTemp += len;
    }
}

HgbResult HgbParser::readVersionInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    readStringParams(pStart, pEnd, isBigEndian, [&](uint32_t id, QString val){
        if (id == 206) headerInfo.instrumentSN = val.toStdString();
        else if (id == 201) headerInfo.version = val.toStdString();
    });
    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readSampleInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    readStringParams(pStart, pEnd, isBigEndian, [&](uint32_t id, QString val){
        if (id == 1) headerInfo.sampleInfo = val.toStdString();
    });
    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readMeasureInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    const uint8_t* pBase = pStart;
    int offset = -1;
    // 智能偏移搜索
    for (int off = 16; off <= 24; off++) {
        if (pBase + off + 8 > pEnd) break;
        uint32_t cnt = readUint32(pBase + off, isBigEndian);
        if (cnt > 0 && cnt < 200) { offset = off; break; }
    }
    if (offset == -1) return HgbResult::DATA_ERROR;
    
    uint32_t count = readUint32(pBase + offset, isBigEndian);
    const uint8_t* pTemp = pBase + offset + 4;
    
    for(uint32_t i=0; i<count && pTemp < pEnd; i++) {
        if (pTemp + 9 > pEnd) break;
        uint32_t id = readUint32(pTemp, isBigEndian); pTemp += 4;
        uint8_t type = *pTemp++;
        uint32_t num = readUint32(pTemp, isBigEndian); pTemp += 4;
        size_t bytes = num * HgbParser::TYPE_LENGTHS[type < 7 ? type : 0];
        if (pTemp + bytes > pEnd) break;
        
        if (id == 4) { // Vet Type
             int16_t val = readInt16(pTemp, isBigEndian);
             if (val == 1) headerInfo.measureInfo += "; Canine";
             else if (val == 2) headerInfo.measureInfo += "; Feline";
             else headerInfo.measureInfo += "; Animal(" + std::to_string(val) + ")";
        }
        pTemp += bytes;
    }
    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readHgbPulse(const uint8_t* pStart, const uint8_t* pEnd,
                                 std::vector<uint16_t>& pulseData, uint32_t& dataLength, bool isBigEndian) {
    // 智能搜索：HGB Pulse 头部特征码
    const uint8_t* pScan = pStart + 16;
    const uint8_t* pFound = nullptr;
    
    for (int i = 0; i < 64 && (pScan + i + 6) < pEnd; i++) {
        if (pScan[i] == 0x01 && pScan[i+2] == 0x14 && 
            pScan[i+3] == 0x01 && pScan[i+4] == 0x03 && pScan[i+5] == 0x02) 
        {
            pFound = pScan + i;
            break;
        }
    }

    if (!pFound) {
        // 如果找不到特征码，尝试默认位置
        pFound = pStart + 18; 
        if (*pFound != 1) return HgbResult::DATA_ERROR; 
    }

    const uint8_t* pCellNum = pFound + 6;
    if (pCellNum + 4 > pEnd) return HgbResult::DATA_ERROR;
    
    uint32_t cellNum = readUint32(pCellNum, isBigEndian);
    
    // 二次确认：智能搜索 CellNum (防填充)
    if (cellNum == 0) {
        for(int pad=1; pad<=16; pad++) {
            if (pCellNum + pad + 4 <= pEnd) {
                uint32_t cand = readUint32(pCellNum + pad, isBigEndian);
                if (cand > 0 && cand < 100000) {
                    if (pCellNum + pad + 4 + (cand * 2) <= pEnd) {
                        cellNum = cand;
                        pCellNum += pad;
                        break;
                    }
                }
            }
        }
    }

    dataLength = cellNum;
    if (cellNum == 0) return HgbResult::SUCCESS;

    const uint8_t* pData = pCellNum + 4;
    if (pData + (cellNum * 2) > pEnd) return HgbResult::DATA_ERROR;

    pulseData.resize(cellNum);
    if (isBigEndian) {
        for (uint32_t i = 0; i < cellNum; i++) pulseData[i] = readUint16(pData + i*2, true);
    } else {
        std::memcpy(pulseData.data(), pData, cellNum * 2);
    }

    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readCalibrationCoef(const QString& fileName, CalibrationCoef& coef) {
    coef.fHGB = 1.0f; coef.fMCV = 1.0f; coef.fPLT = 1.0f; coef.fRBC = 1.0f; coef.fWBC = 1.0f;
    return HgbResult::SUCCESS;
}

uint32_t HgbParser::calculateCrcCcitt(const uint8_t* data, uint32_t length) {
    return 0; 
}

void HgbParser::calculateStatistics(HgbData& measurement) {
    if (!measurement.measurementValues.empty()) {
        const auto& values = measurement.measurementValues;
        auto minMax = std::minmax_element(values.begin(), values.end());
        measurement.minValue = *minMax.first;
        measurement.maxValue = *minMax.second;
        double sum = 0;
        for(auto v : values) sum += v;
        measurement.mean = sum / values.size();
        
        double sqSum = 0;
        for(auto v : values) sqSum += (v - measurement.mean) * (v - measurement.mean);
        measurement.stdDev = std::sqrt(sqSum / values.size());
    }
}

bool HgbParser::checkBoundary(const uint8_t* ptr, const uint8_t* end, size_t size) {
    return ptr && end && (ptr + size <= end);
}