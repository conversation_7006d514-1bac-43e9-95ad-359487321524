#include "HGBPanel.h"
#include "utils/Logger.h"
#include <QMessageBox>
#include <QFileInfo>
#include <QStandardPaths>
#include <QDateTime>
#include <QtConcurrent>
#include <QApplication>
#include <QHeaderView>
#include <QTextStream>

// QtCharts 头文件已在 HGBPanel.h 中包含，此处不再重复，避免冲突

namespace ui {
namespace HGB {

HGBPanel::HGBPanel(QWidget *parent)
    : QWidget(parent)
    , mainLayout_(nullptr)
    , mainSplitter_(nullptr)
    , fileSelectionGroup_(nullptr)
    , filePathEdit_(nullptr)
    , btnSelectFile_(nullptr)
    , operationGroup_(nullptr)
    , btnParseFile_(nullptr)
    , btnParseBatch_(nullptr)
    , btnExportResults_(nullptr)
    , btnClearResults_(nullptr)
    , progressBar_(nullptr)
    , resultsTabWidget_(nullptr)
    , statisticsText_(nullptr)
    , dataTable_(nullptr)
    , logText_(nullptr)
    , chartView_(nullptr)       
    , chart_(nullptr)
    , measurementSeries_(nullptr)
    , blankSeries_(nullptr)
    , parseWatcher_(nullptr)
    , batchWatcher_(nullptr)
    , parser_(std::make_unique<HgbParser>())
{
    setupUI();
    createConnections();
    
    parseWatcher_ = new QFutureWatcher<HgbParseResult>(this);
    batchWatcher_ = new QFutureWatcher<bool>(this);
    
    utils::Logger::instance().info("HGB面板初始化完成");
}

HGBPanel::~HGBPanel() {
    if (parseWatcher_) {
        parseWatcher_->cancel();
        parseWatcher_->waitForFinished();
    }
    if (batchWatcher_) {
        batchWatcher_->cancel();
        batchWatcher_->waitForFinished();
    }
}

void HGBPanel::setupUI() {
    mainLayout_ = new QVBoxLayout(this);
    mainLayout_->setSpacing(10);
    mainLayout_->setContentsMargins(10, 10, 10, 10);
    
    mainSplitter_ = new QSplitter(Qt::Vertical, this);
    
    createFileSelectionGroup();
    createOperationGroup();
    createResultsGroup();
    
    mainSplitter_->addWidget(fileSelectionGroup_);
    mainSplitter_->addWidget(operationGroup_);
    mainSplitter_->addWidget(resultsTabWidget_);
    
    mainSplitter_->setStretchFactor(0, 0); 
    mainSplitter_->setStretchFactor(1, 0); 
    mainSplitter_->setStretchFactor(2, 1); 
    
    mainLayout_->addWidget(mainSplitter_);
}

void HGBPanel::createFileSelectionGroup() {
    fileSelectionGroup_ = new QGroupBox("文件选择", this);
    QHBoxLayout* layout = new QHBoxLayout(fileSelectionGroup_);
    
    filePathEdit_ = new QLineEdit(this);
    filePathEdit_->setPlaceholderText("请选择INF或INF.zip文件...");
    filePathEdit_->setReadOnly(true);
    
    btnSelectFile_ = new QPushButton("选择文件", this);
    btnSelectFile_->setFixedWidth(100);
    
    layout->addWidget(new QLabel("文件路径:", this));
    layout->addWidget(filePathEdit_);
    layout->addWidget(btnSelectFile_);
}

void HGBPanel::createOperationGroup() {
    operationGroup_ = new QGroupBox("操作", this);
    QVBoxLayout* layout = new QVBoxLayout(operationGroup_);
    
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    
    btnParseFile_ = new QPushButton("解析文件", this);
    btnParseBatch_ = new QPushButton("批量解析", this);
    btnExportResults_ = new QPushButton("导出Excel", this);
    btnClearResults_ = new QPushButton("清除结果", this);
    
    btnParseFile_->setEnabled(false);
    btnParseBatch_->setEnabled(false);
    btnExportResults_->setEnabled(false);
    
    buttonLayout->addWidget(btnParseFile_);
    buttonLayout->addWidget(btnParseBatch_);
    buttonLayout->addWidget(btnExportResults_);
    buttonLayout->addWidget(btnClearResults_);
    buttonLayout->addStretch();
    
    progressBar_ = new QProgressBar(this);
    progressBar_->setVisible(false);
    
    layout->addLayout(buttonLayout);
    layout->addWidget(progressBar_);
}

void HGBPanel::createResultsGroup() {
    resultsTabWidget_ = new QTabWidget(this);
    
    // 1. 图表页
    createDataVisualizationGroup();
    resultsTabWidget_->addTab(chartView_, "波形图表"); 

    // 2. 统计报告页
    statisticsText_ = new QTextEdit(this);
    statisticsText_->setReadOnly(true);
    statisticsText_->setStyleSheet("QTextEdit { font-family: 'Segoe UI', 'Microsoft YaHei'; font-size: 11pt; }");
    resultsTabWidget_->addTab(statisticsText_, "解析报告");
    
    // 3. 数据表格页
    dataTable_ = new QTableWidget(this);
    dataTable_->setAlternatingRowColors(true);
    dataTable_->setSelectionBehavior(QAbstractItemView::SelectRows);
    dataTable_->setEditTriggers(QAbstractItemView::NoEditTriggers);
    resultsTabWidget_->addTab(dataTable_, "原始数据");
    
    // 4. 日志页
    logText_ = new QTextEdit(this);
    logText_->setReadOnly(true);
    logText_->setFont(QFont("Consolas", 9));
    resultsTabWidget_->addTab(logText_, "系统日志");
}

void HGBPanel::createDataVisualizationGroup() {
    chart_ = new QtCharts::QChart();
    chart_->setTitle("HGB 脉冲数据可视化");
    chart_->setAnimationOptions(QtCharts::QChart::SeriesAnimations);
    chart_->legend()->setVisible(true);
    chart_->legend()->setAlignment(Qt::AlignBottom);

    measurementSeries_ = new QtCharts::QLineSeries();
    measurementSeries_->setName("测量值 (Measurement)");
    
    blankSeries_ = new QtCharts::QScatterSeries();
    blankSeries_->setName("空白值 (Blank)");
    blankSeries_->setMarkerSize(8);
    blankSeries_->setColor(QColor(255, 127, 0)); 

    chart_->addSeries(measurementSeries_);
    chart_->addSeries(blankSeries_);

    QtCharts::QValueAxis* axisX = new QtCharts::QValueAxis();
    axisX->setTitleText("采样点 (Index)");
    axisX->setLabelFormat("%d");
    axisX->setTickCount(10);

    QtCharts::QValueAxis* axisY = new QtCharts::QValueAxis();
    axisY->setTitleText("电压值 (ADC)");
    axisY->setLabelFormat("%d");

    chart_->addAxis(axisX, Qt::AlignBottom);
    chart_->addAxis(axisY, Qt::AlignLeft);

    measurementSeries_->attachAxis(axisX);
    measurementSeries_->attachAxis(axisY);
    blankSeries_->attachAxis(axisX);
    blankSeries_->attachAxis(axisY);

    chartView_ = new QtCharts::QChartView(chart_, this);
    chartView_->setRenderHint(QPainter::Antialiasing);
    chartView_->setRubberBand(QtCharts::QChartView::RectangleRubberBand); 
}

void HGBPanel::createConnections() {
    connect(btnSelectFile_, &QPushButton::clicked, this, &HGBPanel::onSelectFileClicked);
    connect(btnParseFile_, &QPushButton::clicked, this, &HGBPanel::onParseFileClicked);
    connect(btnParseBatch_, &QPushButton::clicked, this, &HGBPanel::onParseBatchClicked);
    connect(btnExportResults_, &QPushButton::clicked, this, &HGBPanel::onExportResultsClicked);
    connect(btnClearResults_, &QPushButton::clicked, this, &HGBPanel::onClearResultsClicked);

    connect(parseWatcher_, &QFutureWatcher<HgbParseResult>::finished,
            this, &HGBPanel::onParseFileFinished);
    connect(batchWatcher_, &QFutureWatcher<bool>::finished,
            this, &HGBPanel::onParseBatchFinished);
}

void HGBPanel::setCurrentFile(const QString& path) {
    currentFilePath_ = path;
    filePathEdit_->setText(path);

    QFileInfo fileInfo(path);
    QString suffix = fileInfo.suffix().toLower();

    bool isValidFile = (suffix == "inf" || suffix == "zip");
    btnParseFile_->setEnabled(isValidFile);

    if (isValidFile) {
        logText_->append(QString("[%1] 已加载文件: %2")
                        .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                        .arg(fileInfo.fileName()));
    }
}

void HGBPanel::clearResults() {
    statisticsText_->clear();
    dataTable_->clear();
    dataTable_->setRowCount(0);
    dataTable_->setColumnCount(0);
    logText_->clear();

    if (measurementSeries_) measurementSeries_->clear();
    if (blankSeries_) blankSeries_->clear();
    if (chart_ && chart_->axes(Qt::Horizontal).first()) {
        chart_->axes(Qt::Horizontal).first()->setRange(0, 100);
        chart_->axes(Qt::Vertical).first()->setRange(0, 100);
    }

    batchResults_.clear();
    hasSingleResult_ = false;
    btnExportResults_->setEnabled(false);

    utils::Logger::instance().info("界面已重置");
}

void HGBPanel::onSelectFileClicked() {
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择HGB数据文件",
        QDir::currentPath(), 
        "HGB数据文件 (*.inf *.zip);;INF文件 (*.inf);;ZIP文件 (*.zip);;所有文件 (*.*)"
    );

    if (!fileName.isEmpty()) {
        setCurrentFile(fileName);
    }
}

void HGBPanel::onParseFileClicked() {
    if (currentFilePath_.isEmpty()) {
        QMessageBox::warning(this, "警告", "请先选择要解析的文件");
        return;
    }

    btnParseFile_->setEnabled(false);
    progressBar_->setVisible(true);
    progressBar_->setRange(0, 0); 

    logText_->append(QString("[%1] 开始解析...").arg(QDateTime::currentDateTime().toString("hh:mm:ss")));

    QString path = currentFilePath_;

    QFuture<HgbParseResult> future = QtConcurrent::run([this, path]() {
        QFileInfo fileInfo(path);
        QString suffix = fileInfo.suffix().toLower();
        
        if (suffix == "zip") {
            return parser_->parseInfZipFile(path);
        } else {
            return parser_->parseInfFile(path);
        }
    });

    parseWatcher_->setFuture(future);
}

void HGBPanel::onParseFileFinished() {
    currentSingleResult_ = parseWatcher_->result();
    hasSingleResult_ = true;

    btnParseFile_->setEnabled(true);
    progressBar_->setVisible(false);

    QString timeStr = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString status = (currentSingleResult_.result == HgbResult::SUCCESS) ? "成功" : "失败";
    
    logText_->append(QString("[%1] 解析%2").arg(timeStr).arg(status));

    if (currentSingleResult_.result != HgbResult::SUCCESS) {
        QString err = QString::fromStdString(currentSingleResult_.errorMessage);
        logText_->append(QString("错误详情: %1").arg(err));
        QMessageBox::critical(this, "解析失败", err);
        btnExportResults_->setEnabled(false);
    } else {
        updateResultsDisplay(currentSingleResult_);
        btnExportResults_->setEnabled(true);
        emit dataParseCompleted(currentSingleResult_);
        
        resultsTabWidget_->setCurrentIndex(0); 
    }
}

void HGBPanel::onParseBatchClicked() {
    if (filePaths_.isEmpty() && !currentFilePath_.isEmpty()) {
        filePaths_.append(currentFilePath_);
    }

    if (filePaths_.isEmpty()) {
        QMessageBox::warning(this, "警告", "没有文件可处理");
        return;
    }

    batchResults_.clear();
    btnParseBatch_->setEnabled(false);
    progressBar_->setVisible(true);
    progressBar_->setRange(0, filePaths_.size());
    progressBar_->setValue(0);

    emit batchProcessStarted(filePaths_.size());

    QFuture<bool> future = QtConcurrent::run([this]() {
        return processBatchFiles();
    });

    batchWatcher_->setFuture(future);
}

void HGBPanel::onParseBatchFinished() {
    bool success = batchWatcher_->result();
    btnParseBatch_->setEnabled(true);
    progressBar_->setVisible(false);
    logText_->append("批量处理完成");
    
    if (success && !batchResults_.empty()) {
        btnExportResults_->setEnabled(true);
        
        int totalFiles = batchResults_.size();
        int totalMeasurements = 0;
        double totalHgb = 0.0;
        int successCount = 0;
        
        for(const auto& res : batchResults_) {
            if(res.result == HgbResult::SUCCESS) {
                totalMeasurements += res.hgbData.measurementCount;
                totalHgb += res.calculateHgbConcentration();
                successCount++;
            }
        }
        
        double avgHgb = successCount > 0 ? (totalHgb / successCount) : 0.0;
        QMap<QString, int> stats;
        stats["Successful"] = successCount;
        stats["Failed"] = totalFiles - successCount;
        
        emit batchStatisticsReady("HGB Analysis", totalFiles, totalMeasurements, avgHgb, stats);
    }
    
    emit batchProcessFinished();
}

void HGBPanel::onExportResultsClicked() {
    QString defaultName = "HGB_Export_" + QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".csv";
    
    QString fileName = QFileDialog::getSaveFileName(
        this,
        "导出HGB解析结果",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/" + defaultName,
        "CSV文件 (*.csv)"
    );

    if (!fileName.isEmpty()) {
        exportResultsToCSV(fileName);
    }
}

void HGBPanel::onClearResultsClicked() {
    clearResults();
}

void HGBPanel::updateResultsDisplay(const HgbParseResult& result) {
    updateStatisticsDisplay(result);
    updateDataTable(result);
    updateChart(result); 
}

void HGBPanel::updateChart(const HgbParseResult& result) {
    if (!measurementSeries_ || !blankSeries_) return;

    measurementSeries_->clear();
    blankSeries_->clear();

    const auto& measurements = result.hgbData.measurementValues;
    const auto& blanks = result.hgbData.blankValues;

    uint16_t minY = 65535, maxY = 0;
    
    for (size_t i = 0; i < measurements.size(); i++) {
        measurementSeries_->append(i, measurements[i]);
        if (measurements[i] < minY) minY = measurements[i];
        if (measurements[i] > maxY) maxY = measurements[i];
    }

    for (size_t i = 0; i < blanks.size(); i++) {
        blankSeries_->append(i, blanks[i]);
        if (blanks[i] < minY) minY = blanks[i];
        if (blanks[i] > maxY) maxY = blanks[i];
    }

    int maxPoints = std::max(measurements.size(), blanks.size());
    if (maxPoints == 0) maxPoints = 100;

    auto axisX = qobject_cast<QtCharts::QValueAxis*>(chart_->axes(Qt::Horizontal).first());
    if (axisX) axisX->setRange(0, maxPoints > 0 ? maxPoints - 1 : 0);

    auto axisY = qobject_cast<QtCharts::QValueAxis*>(chart_->axes(Qt::Vertical).first());
    if (axisY) {
        if (minY == 65535) { minY = 0; maxY = 1000; }
        double range = maxY - minY;
        if (range == 0) range = 10.0;
        axisY->setRange(std::max(0.0, minY - range * 0.1), maxY + range * 0.1);
    }
}

void HGBPanel::updateStatisticsDisplay(const HgbParseResult& result) {
    QString sampleInfo = QString::fromStdString(result.headerInfo.sampleInfo);
    QString rawMeasureInfo = QString::fromStdString(result.headerInfo.measureInfo);
    QStringList infoParts = rawMeasureInfo.split(";", Qt::SkipEmptyParts);
    QString sampleType = infoParts.value(0, "Unknown").trimmed();
    QString species = infoParts.value(1, "Unknown").trimmed();
    QString gender = infoParts.value(2, "Unknown").trimmed();
    double hgbValue = result.calculateHgbConcentration();
    
    QString html = R"(
    <style>
        h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
        .highlight { color: #e74c3c; font-weight: bold; font-size: 24px; }
        .section { margin-top: 15px; font-weight: bold; color: #34495e; }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        td { padding: 5px; border-bottom: 1px solid #ecf0f1; }
        .label { color: #7f8c8d; width: 120px; }
        .value { color: #2c3e50; font-weight: bold; }
    </style>
    )";

    html += "<h2>HGB 分析报告</h2>";
    html += "<div>HGB 浓度: <span class='highlight'>" + QString::number(hgbValue, 'f', 1) + " g/dL</span></div>";
    html += "<div class='section'>样本信息</div>";
    html += "<table>";
    html += "<tr><td class='label'>样本编号:</td><td class='value'>" + sampleInfo + "</td></tr>";
    html += "<tr><td class='label'>样本类型:</td><td class='value'>" + sampleType + "</td></tr>";
    html += "<tr><td class='label'>动物种类:</td><td class='value'>" + species + "</td></tr>";
    html += "<tr><td class='label'>性别:</td><td class='value'>" + gender + "</td></tr>";
    html += "<tr><td class='label'>仪器SN:</td><td class='value'>" + QString::fromStdString(result.headerInfo.instrumentSN) + "</td></tr>";
    html += "</table>";

    html += "<div class='section'>统计数据</div>";
    html += "<table>";
    html += "<tr><td class='label'>总粒子数:</td><td class='value'>" + QString::number(result.hgbData.measurementCount) + "</td></tr>";
    html += "<tr><td class='label'>平均电压:</td><td class='value'>" + QString::number(result.hgbData.mean, 'f', 2) + "</td></tr>";
    double cv = (result.hgbData.mean > 0) ? (result.hgbData.stdDev / result.hgbData.mean * 100.0) : 0.0;
    html += "<tr><td class='label'>变异系数 (CV):</td><td class='value'>" + QString::number(cv, 'f', 2) + "%</td></tr>";
    html += "</table>";

    statisticsText_->setHtml(html);
}

void HGBPanel::updateDataTable(const HgbParseResult& result) {
    const auto& measurements = result.hgbData.measurementValues;
    const auto& blanks = result.hgbData.blankValues;

    dataTable_->setColumnCount(3);
    dataTable_->setHorizontalHeaderLabels({"序号", "测量电压", "空白电压"});
    
    int rowCount = std::max(measurements.size(), blanks.size());
    dataTable_->setSortingEnabled(false);
    dataTable_->setUpdatesEnabled(false);
    dataTable_->setRowCount(rowCount);

    for (int i = 0; i < rowCount; i++) {
        QTableWidgetItem* itemIdx = new QTableWidgetItem();
        itemIdx->setData(Qt::DisplayRole, i + 1);
        dataTable_->setItem(i, 0, itemIdx);

        if (i < (int)measurements.size()) {
            QTableWidgetItem* itemVal = new QTableWidgetItem();
            itemVal->setData(Qt::DisplayRole, measurements[i]);
            dataTable_->setItem(i, 1, itemVal);
        }

        if (i < (int)blanks.size()) {
            QTableWidgetItem* itemBlank = new QTableWidgetItem();
            itemBlank->setData(Qt::DisplayRole, blanks[i]);
            dataTable_->setItem(i, 2, itemBlank);
        }
    }
    
    dataTable_->setUpdatesEnabled(true);
    dataTable_->setSortingEnabled(true); 
    
    dataTable_->horizontalHeader()->setSectionResizeMode(0, QHeaderView::ResizeToContents);
    dataTable_->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Stretch);
    dataTable_->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Stretch);
}

void HGBPanel::exportResultsToCSV(const QString& filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "错误", "无法创建文件");
        return;
    }

    QTextStream out(&file);
    out << "\xEF\xBB\xBF"; 

    if (hasSingleResult_ && batchResults_.empty()) {
        out << "文件信息:," << currentFilePath_ << "\n";
        out << "解析时间:," << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "\n";
        out << "样本ID:," << QString::fromStdString(currentSingleResult_.headerInfo.sampleInfo) << "\n";
        out << "\n";
        out << "序号,测量电压(Pulse),空白电压(Blank)\n";
        
        const auto& meas = currentSingleResult_.hgbData.measurementValues;
        const auto& blank = currentSingleResult_.hgbData.blankValues;
        size_t maxRows = std::max(meas.size(), blank.size());
        
        for (size_t i = 0; i < maxRows; ++i) {
            out << (i + 1) << ",";
            if (i < meas.size()) out << meas[i];
            out << ",";
            if (i < blank.size()) out << blank[i];
            out << "\n";
        }
    } 
    else if (!batchResults_.empty()) {
        out << "文件名,HGB浓度,总粒子数,平均电压,CV%\n";
        for (const auto& res : batchResults_) {
            double cv = (res.hgbData.mean > 0) ? (res.hgbData.stdDev / res.hgbData.mean * 100.0) : 0.0;
            out << QString::fromStdString(res.headerInfo.sampleInfo) << ","
                << QString::number(res.calculateHgbConcentration(), 'f', 2) << ","
                << res.hgbData.measurementCount << ","
                << QString::number(res.hgbData.mean, 'f', 2) << ","
                << QString::number(cv, 'f', 2) << "\n";
        }
    }

    file.close();
    QMessageBox::information(this, "导出成功", "数据已成功保存。");
}

void HGBPanel::updateBatchProgress(int current, int total) {
    progressBar_->setValue(current);
    emit batchProcessProgress(current, total);
}

bool HGBPanel::processBatchFiles() {
    batchResults_.clear();
    for (int i = 0; i < filePaths_.size(); i++) {
        if (QThread::currentThread()->isInterruptionRequested()) return false;

        const QString& filePath = filePaths_[i];
        QFileInfo fileInfo(filePath);
        QString suffix = fileInfo.suffix().toLower();

        if (suffix != "inf" && suffix != "zip") continue;

        HgbParseResult result;
        if (suffix == "zip") {
            result = parser_->parseInfZipFile(filePath);
        } else {
            result = parser_->parseInfFile(filePath);
        }

        if (result.headerInfo.sampleInfo.empty()) {
            result.headerInfo.sampleInfo = fileInfo.fileName().toStdString();
        }
        
        batchResults_.push_back(result);

        QMetaObject::invokeMethod(this, "updateBatchProgress",
                                 Qt::QueuedConnection,
                                 Q_ARG(int, i + 1),
                                 Q_ARG(int, filePaths_.size()));
    }
    return true;
}

} // namespace HGB
} // namespace ui