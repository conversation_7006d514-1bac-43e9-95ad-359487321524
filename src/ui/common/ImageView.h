#ifndef IMAGEVIEW_H
#define IMAGEVIEW_H

#include <QWidget>
#include <QLabel>
#include <QTabWidget>
#include <QScrollArea>
#include <QWheelEvent>
#include <QPoint>
#include <opencv2/core.hpp>
#include "modules/detection/DetectionTypes.h"
#include "ui/common/PerformanceResultsWidget.h"
#include "ui/common/DetectionResultsPanel.h"
#include "ui/common/AnnotationResultsPanel.h"

class ImageView : public QWidget {
    Q_OBJECT
public:
    explicit ImageView(QWidget *parent = nullptr);
    ~ImageView();

    void setImage(const cv::Mat& image, const QString& imagePath = "");

    // 性能指标相关
    void setPerformanceMetrics(const EvaluationMetrics& metrics);
    void showPerformanceTab();

    // 检测结果相关
    void setDetections(const QList<Detection>& detections);

    // 标注结果相关
    void setAnnotations(const QList<Detection>& annotations);

    // 算法类型相关
    void setCurrentAlgorithm(const QString& algorithm);

    // 获取当前显示的图像（包含检测/标注结果）
    cv::Mat getCurrentDisplayImage() const;

protected:
    void wheelEvent(QWheelEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;
    bool eventFilter(QObject* obj, QEvent* event) override;

private:
    void setupUI();
    void createConnections();
    void updateZoom(double factor, const QPoint& center);
    void zoomAtPoint(double newZoomFactor, const QPoint& centerPoint);

    // 主标签页控件
    QTabWidget* tabWidget_;

    // 图像显示相关
    QScrollArea* scrollArea_;
    QLabel* imageLabel_;
    QWidget* imageTab_;

    // 检测结果相关
    ui::common::DetectionResultsPanel* detectionResultsPanel_;

    // 标注结果相关
    ui::common::AnnotationResultsPanel* annotationResultsPanel_;

    // 缩放和平移相关
    double zoomFactor_{1.0};
    bool isPanning_{false};
    QPoint lastPanPos_;
    cv::Mat originalImage_;

    // 当前检测结果
    QList<Detection> currentDetections_;

    // 当前标注结果
    QList<Detection> currentAnnotations_;

    // 当前图像路径
    QString currentImagePath_;

    // 当前算法类型
    QString currentAlgorithm_;
};

#endif // IMAGEVIEW_H