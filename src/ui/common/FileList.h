#ifndef FILELIST_H
#define FILELIST_H

#include <QWidget>
#include <QListWidget>
#include <QStringList>

class FileList : public QWidget {
    Q_OBJECT
public:
    explicit FileList(QWidget *parent = nullptr);
    ~FileList();

    // 获取控件
    QListWidget* listWidget() const { return listWidget_; }

    // 文件操作
    void clear();
    void addFiles(const QStringList& files);
    void setCurrentIndex(int index);
    int currentIndex() const;
    QString currentFile() const;
    QStringList files() const;

signals:
    void currentFileChanged(const QString& file);
    void fileListChanged(const QStringList& files);

private:
    void setupUI();
    void createConnections();

    QListWidget* listWidget_;
    QStringList fullPathList_;
};

#endif // FILELIST_H 