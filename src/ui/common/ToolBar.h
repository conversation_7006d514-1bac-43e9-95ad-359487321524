#ifndef TOOLBAR_H
#define TOOLBAR_H

#include <QWidget>
#include <QToolBar>
#include <QPushButton>

class ToolBar : public QWidget {
    Q_OBJECT
public:
    explicit ToolBar(QWidget *parent = nullptr);
    ~ToolBar();

    // 基础导航按钮的访问器
    QPushButton* btnOpenImage() const { return btnOpenImage_; }
    QPushButton* btnOpenFolder() const { return btnOpenFolder_; }
    QPushButton* btnPrev() const { return btnPrev_; }
    QPushButton* btnNext() const { return btnNext_; }

    // 保存图片按钮访问器
    QPushButton* btnSaveImage() const { return btnSaveImage_; }

    // 更新时间戳按钮访问器
    QPushButton* btnUpdateTimestamp() const { return btnUpdateTimestamp_; }

signals:
    // 基础导航相关信号
    void openImageClicked();
    void openFolderClicked();
    void prevClicked();
    void nextClicked();

    // 保存图片信号
    void saveImageClicked();

    // 更新时间戳信号
    void updateTimestampClicked();

private:
    void setupUI();
    void createConnections();

    // 基础导航按钮
    QPushButton* btnOpenImage_;
    QPushButton* btnOpenFolder_;
    QPushButton* btnPrev_;
    QPushButton* btnNext_;

    // 保存图片按钮
    QPushButton* btnSaveImage_;

    // 更新时间戳按钮
    QPushButton* btnUpdateTimestamp_;
};

#endif // TOOLBAR_H