#include "ui/common/AnnotationResultsPanel.h"
#include "utils/Logger.h"
#include "utils/ImageIO.h"
#include "utils/ConfigManager.h"
#include <QPixmap>
#include <QImage>
#include <QFileInfo>
#include <QHBoxLayout>
#include <opencv2/imgproc.hpp>

namespace ui {
namespace common {

AnnotationResultsPanel::AnnotationResultsPanel(QWidget *parent) : QWidget(parent) {
    setupUI();
}

AnnotationResultsPanel::~AnnotationResultsPanel() {
}

void AnnotationResultsPanel::setupUI() {
    // 创建主布局
    mainLayout_ = new QVBoxLayout(this);
    mainLayout_->setContentsMargins(0, 0, 0, 0);
    mainLayout_->setSpacing(10);

    // 创建标题标签
    QLabel* titleLabel = new QLabel("标注结果分类", this);
    titleLabel->setAlignment(Qt::AlignCenter);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);

    // 创建说明标签
    QLabel* descLabel = new QLabel("按类别显示所有标注目标", this);
    descLabel->setAlignment(Qt::AlignCenter);
    QFont descFont = descLabel->font();
    descFont.setPointSize(10);
    descLabel->setFont(descFont);

    // 创建滚动区域
    scrollArea_ = new QScrollArea(this);
    scrollArea_->setWidgetResizable(true);
    scrollArea_->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea_->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 创建滚动内容
    scrollContent_ = new QWidget(scrollArea_);
    contentLayout_ = new QVBoxLayout(scrollContent_);
    contentLayout_->setAlignment(Qt::AlignTop);
    contentLayout_->setSpacing(20);
    scrollContent_->setLayout(contentLayout_);

    scrollArea_->setWidget(scrollContent_);

    // 添加组件到主布局
    mainLayout_->addWidget(titleLabel);
    mainLayout_->addWidget(descLabel);
    mainLayout_->addWidget(scrollArea_);
}

void AnnotationResultsPanel::setAnnotations(const QList<Detection>& annotations, const cv::Mat& sourceImage, const QString& imagePath) {
    // 保存标注结果、源图像和图像路径
    annotations_ = annotations;
    currentImagePath_ = imagePath;

    // 记录一些标注信息用于调试
    for (int i = 0; i < std::min<int>(5, static_cast<int>(annotations.size())); i++) {
        const auto& anno = annotations[i];
        utils::Logger::instance().debug(QString("标注 #%1: 类别=%2, 边界框=[%3,%4,%5,%6], 图像=%7")
            .arg(i)
            .arg(QString::fromStdString(anno.label))
            .arg(anno.bbox.x).arg(anno.bbox.y)
            .arg(anno.bbox.width).arg(anno.bbox.height)
            .arg(QString::fromStdString(anno.imagePath)));
    }

    // 确保源图像有效
    if (sourceImage.empty()) {
        utils::Logger::instance().warning("源图像为空，无法显示标注结果");
        return;
    }

    // 创建深拷贝
    sourceImage_ = sourceImage.clone();
    utils::Logger::instance().debug(QString("源图像尺寸: %1 x %2").arg(sourceImage_.cols).arg(sourceImage_.rows));

    // 显示标注结果
    displayAnnotations();
}

void AnnotationResultsPanel::clearResults() {
    // 清除所有类别分组
    for (auto it = categoryGroups_.begin(); it != categoryGroups_.end(); ++it) {
        delete it.value();
    }

    categoryGroups_.clear();
    categoryLayouts_.clear();

    // 不要清空标注结果和源图像
    // annotations_.clear();
    // sourceImage_ = cv::Mat();
}

void AnnotationResultsPanel::displayAnnotations() {
    // 清除之前的结果
    clearResults();

    // 检查源图像是否有效
    if (sourceImage_.empty() || annotations_.isEmpty()) {
        utils::Logger::instance().warning("源图像为空或标注结果为空，无法显示标注结果");
        return;
    }

    // 获取每个类别的最大显示数量
    int maxDisplayPerCategory = utils::ConfigManager::instance().getMaxDisplayPerCategory();

    // 按类别分组标注结果，并限制每个类别的显示数量
    QMap<QString, QList<Detection>> categoryAnnotations;
    QMap<QString, int> categoryTotalCounts; // 记录每个类别的总数量

    for (const auto& annotation : annotations_) {
        QString category = QString::fromStdString(annotation.label);
        categoryTotalCounts[category]++;

        // 只添加到显示列表中，如果还没有达到限制
        if (categoryAnnotations[category].size() < maxDisplayPerCategory) {
            categoryAnnotations[category].append(annotation);
        }
    }

    // 创建图像缓存，避免重复加载相同的图像
    QMap<QString, cv::Mat> imageCache;

    // 按类别分组显示标注结果
    for (auto it = categoryAnnotations.begin(); it != categoryAnnotations.end(); ++it) {
        const QString& category = it.key();
        const QList<Detection>& annotationsToShow = it.value();
        int totalCount = categoryTotalCounts[category];

        // 为每个类别创建分组
        createCategoryGroup(category);

        // 更新类别分组标题，显示总数和当前显示数
        QString groupTitle;
        if (totalCount > maxDisplayPerCategory) {
            groupTitle = QString("%1 (显示 %2/%3)").arg(category).arg(annotationsToShow.size()).arg(totalCount);
        } else {
            groupTitle = QString("%1 (%2)").arg(category).arg(totalCount);
        }
        categoryGroups_[category]->setTitle(groupTitle);

        // 处理该类别中要显示的标注结果
        for (const auto& annotation : annotationsToShow) {

        // 获取要裁切的图像
        cv::Mat imageToProcess;

        // 如果标注结果包含图像路径，则从该路径加载图像
        if (!annotation.imagePath.empty()) {
            QString imagePath = QString::fromStdString(annotation.imagePath);

            // 检查图像是否已经在缓存中
            if (imageCache.contains(imagePath)) {
                imageToProcess = imageCache[imagePath];
            } else {
                imageToProcess = utils::ImageIO::imageRead(imagePath);
                if (imageToProcess.empty()) {
                    utils::Logger::instance().warning(QString("无法加载图像: %1").arg(imagePath));
                    continue;
                }

                // 将图像添加到缓存
                imageCache[imagePath] = imageToProcess.clone();
            }
        } else {
            // 使用当前源图像
            if (sourceImage_.empty()) {
                utils::Logger::instance().warning("源图像为空，无法裁切");
                continue;
            }
            imageToProcess = sourceImage_;
        }

        // 裁切标注目标
        QPixmap pixmap;
        try {
            // 获取边界框并转换坐标
            cv::Rect bbox = annotation.bbox;

            // 转换标注坐标 - 标注数据存储为归一化值乘以1000
            int imgWidth = imageToProcess.cols;
            int imgHeight = imageToProcess.rows;

            // 转换坐标
            bbox.x = static_cast<int>((bbox.x / 1000.0f) * imgWidth);
            bbox.y = static_cast<int>((bbox.y / 1000.0f) * imgHeight);
            bbox.width = static_cast<int>((bbox.width / 1000.0f) * imgWidth);
            bbox.height = static_cast<int>((bbox.height / 1000.0f) * imgHeight);

            // 记录转换后的坐标
            utils::Logger::instance().debug(QString("标注边界框: [x=%1, y=%2, w=%3, h=%4]")
                .arg(bbox.x).arg(bbox.y)
                .arg(bbox.width).arg(bbox.height));

            // 确保边界框在图像范围内
            bbox.x = std::max(0, bbox.x);
            bbox.y = std::max(0, bbox.y);
            bbox.width = std::min(bbox.width, imageToProcess.cols - bbox.x);
            bbox.height = std::min(bbox.height, imageToProcess.rows - bbox.y);

            // 检查边界框是否有效
            if (bbox.width <= 0 || bbox.height <= 0) {
                utils::Logger::instance().warning(QString("无效的边界框: x=%1, y=%2, w=%3, h=%4")
                                          .arg(bbox.x).arg(bbox.y).arg(bbox.width).arg(bbox.height));
                continue;
            }

            // 裁切图像
            cv::Mat cropped = imageToProcess(bbox);

            // 计算缩放比例，确保裁切图像不超过最大尺寸
            double scale = 1.0;
            if (cropped.cols > MAX_CROP_SIZE || cropped.rows > MAX_CROP_SIZE) {
                double scaleX = static_cast<double>(MAX_CROP_SIZE) / cropped.cols;
                double scaleY = static_cast<double>(MAX_CROP_SIZE) / cropped.rows;
                scale = std::min(scaleX, scaleY);
                utils::Logger::instance().debug(QString("标注结果需要缩放: 原始尺寸=%1x%2, 缩放比例=%3")
                    .arg(cropped.cols).arg(cropped.rows).arg(scale));
            } else {
                utils::Logger::instance().debug(QString("标注结果无需缩放: 尺寸=%1x%2")
                    .arg(cropped.cols).arg(cropped.rows));
            }

            // 缩放裁切图像
            cv::Mat resized;
            cv::resize(cropped, resized, cv::Size(), scale, scale, cv::INTER_AREA);

            // 转换为QPixmap
            cv::Mat rgb;
            cv::cvtColor(resized, rgb, cv::COLOR_BGR2RGB);
            QImage qimg(rgb.data, rgb.cols, rgb.rows, rgb.step, QImage::Format_RGB888);
            pixmap = QPixmap::fromImage(qimg);

            if (pixmap.isNull()) {
                continue;
            }

        } catch (const cv::Exception& e) {
            utils::Logger::instance().error(QString("OpenCV异常: %1").arg(e.what()));
            continue;
        } catch (const std::exception& e) {
            utils::Logger::instance().error(QString("标准异常: %1").arg(e.what()));
            continue;
        } catch (...) {
            utils::Logger::instance().error("未知异常");
            continue;
        }

        // 创建标签文本
        QString labelText;
        QString sourceImageName;

        if (!annotation.imagePath.empty()) {
            // 如果有图像路径，获取文件名
            QFileInfo fileInfo(QString::fromStdString(annotation.imagePath));
            sourceImageName = fileInfo.fileName();
            labelText = QString("%1").arg(category);
        } else {
            labelText = QString("%1").arg(category);
        }

            // 添加到对应类别分组
            addCroppedImageToCategory(category, pixmap, labelText, sourceImageName);
        }
    }

    // 更新UI
    scrollContent_->updateGeometry();
    scrollArea_->updateGeometry();
}

QGroupBox* AnnotationResultsPanel::createCategoryGroup(const QString& category) {
    // 检查类别分组是否已存在
    if (categoryGroups_.contains(category)) {
        return categoryGroups_[category];
    }

    // 创建类别分组
    QGroupBox* groupBox = new QGroupBox(scrollContent_);

    // 设置类别名称（标题将在displayAnnotations中设置）
    groupBox->setTitle(category);

    // 创建垂直布局作为主布局
    QVBoxLayout* groupLayout = new QVBoxLayout(groupBox);
    groupLayout->setContentsMargins(10, 15, 10, 10);
    groupLayout->setSpacing(10);

    // 创建水平布局用于放置裁切图像
    QHBoxLayout* imagesLayout = new QHBoxLayout();
    imagesLayout->setSpacing(30); // 设置固定间距
    imagesLayout->setContentsMargins(5, 10, 5, 5);
    imagesLayout->setAlignment(Qt::AlignLeft | Qt::AlignTop); // 靠左上对齐

    // 添加弹性空间，防止单个类别时布局被拉伸
    imagesLayout->addStretch();

    // 添加流式布局到主布局
    groupLayout->addLayout(imagesLayout);

    // 保存类别分组和布局
    categoryGroups_[category] = groupBox;
    categoryLayouts_[category] = imagesLayout;

    // 添加到内容布局
    contentLayout_->addWidget(groupBox);

    return groupBox;
}

void AnnotationResultsPanel::addCroppedImageToCategory(const QString& category, const QPixmap& pixmap, const QString& label, const QString& sourceImageName) {
    // 确保类别分组存在
    if (!categoryGroups_.contains(category)) {
        createCategoryGroup(category);
    }

    // 检查pixmap是否有效
    if (pixmap.isNull()) {
        return;
    }

    // 创建图像容器
    QWidget* imageContainer = new QWidget();
    QVBoxLayout* containerLayout = new QVBoxLayout(imageContainer);
    containerLayout->setContentsMargins(5, 5, 5, 5);
    containerLayout->setSpacing(5);
    containerLayout->setAlignment(Qt::AlignTop | Qt::AlignHCenter); // 设置顶部居中对齐

    // 设置容器的固定宽度，确保有足够的空间显示文件名
    int containerWidth = std::max(200, pixmap.width() + 40);
    imageContainer->setFixedWidth(containerWidth);

    // 创建图像标签
    QLabel* imageLabel = new QLabel();
    imageLabel->setPixmap(pixmap);
    imageLabel->setAlignment(Qt::AlignCenter);
    imageLabel->setFixedSize(pixmap.width() + 10, pixmap.height() + 10);
    imageLabel->setFrameShape(QFrame::Box);
    imageLabel->setFrameShadow(QFrame::Sunken);

    // 添加一些弹性空间，使图像标签位于顶部
    QSpacerItem* verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);
    containerLayout->addItem(verticalSpacer);

    // 设置图像标签的样式
    imageLabel->setStyleSheet("border: 1px solid #cccccc; border-radius: 4px; background-color: #f9f9f9;");

    // 不创建类别标签，只在分组标题中显示类别

    // 创建来源图像名标签
    QLabel* sourceLabel = nullptr;
    if (!sourceImageName.isEmpty()) {
        sourceLabel = new QLabel(QString("%1").arg(sourceImageName));
        sourceLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        sourceLabel->setWordWrap(true);
        sourceLabel->setMinimumWidth(containerWidth - 10); // 确保文本标签有足够的宽度
        sourceLabel->setContentsMargins(5, 0, 0, 0); // 添加左边距，使文本与图像边缘对齐

        // 设置来源标签的样式
        QFont sourceFont = sourceLabel->font();
        sourceFont.setPointSize(9); // 设置字体大小稍小
        sourceFont.setItalic(false); // 不使用斜体
        sourceLabel->setFont(sourceFont);
        // sourceLabel->setStyleSheet("color: #000000;"); // 设置为黑色
    }

    // 设置容器的最大高度，防止过度拉伸
    imageContainer->setMaximumHeight(pixmap.height() + 100);

    // 添加到容器 - 先添加图像，然后是文件名
    containerLayout->addWidget(imageLabel);
    if (sourceLabel) {
        containerLayout->addWidget(sourceLabel);
    }

    // 添加弹性空间，防止容器被过度拉伸
    containerLayout->addStretch();

    // 设置容器的样式
    imageContainer->setStyleSheet("background-color: transparent;");

    // 添加到类别布局（使用水平布局）
    QHBoxLayout* hboxLayout = categoryLayouts_[category];

    // 在添加新的图像容器之前，先移除之前添加的弹性空间
    if (hboxLayout->count() > 0) {
        QLayoutItem* lastItem = hboxLayout->itemAt(hboxLayout->count() - 1);
        if (lastItem && lastItem->spacerItem()) {
            hboxLayout->removeItem(lastItem);
            delete lastItem;
        }
    }

    hboxLayout->addWidget(imageContainer);

    // 重新添加弹性空间到末尾，保持左对齐
    hboxLayout->addStretch();

    // 标题现在在displayAnnotations中统一设置，不在这里更新
}

} // namespace common
} // namespace ui
