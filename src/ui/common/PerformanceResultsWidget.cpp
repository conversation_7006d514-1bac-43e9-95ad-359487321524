#include "ui/common/PerformanceResultsWidget.h"
#include <QHeaderView>
#include <QFont>
#include <QColor>
#include <QBrush>
#include <QFrame>
#include <QDateTime>
#include <QPainter>
#include <QTransform>
#include <QPixmap>
#include <QRegularExpression>
#include <set>


namespace ui {
namespace common {

PerformanceResultsWidget::PerformanceResultsWidget(QWidget *parent)
    : QWidget(parent)
    , tabWidget_(nullptr)
    , metricsTableWidget_(nullptr)
    , confusionMatrixPlot_(nullptr)
    , confusionMatrixWidget_(nullptr)
    , overviewWidget_(nullptr)
    , overviewGridLayout_(nullptr)
    , classDistributionPlot_(nullptr)
    , performanceBarPlot_(nullptr)
    , predictionStatsPlot_(nullptr)
    , performanceWidget_(nullptr)
    , classPerformancePlot_(nullptr)
    , validationStatsWidget_(nullptr)
    , validationStatsLabel_(nullptr)
    , mainLayout_(nullptr)
{
    // 创建主布局
    mainLayout_ = new QVBoxLayout(this);
    setLayout(mainLayout_);

    // 创建标签页组件
    tabWidget_ = new QTabWidget(this);
    mainLayout_->addWidget(tabWidget_);

    // 创建性能总览面板
    createOverviewPanel();

    // 创建性能指标表格
    createMetricsTable();

    // 创建混淆矩阵表格
    createConfusionMatrixTable();

    // 创建验证数据统计信息区域
    createValidationStatsWidget();

    // 添加验证数据统计信息区域到主布局
    mainLayout_->addWidget(validationStatsWidget_);
}

PerformanceResultsWidget::~PerformanceResultsWidget()
{
    // Qt会自动删除子组件，无需手动释放内存
}

void PerformanceResultsWidget::createMetricsTable()
{
    // 创建性能指标面板
    performanceWidget_ = new QWidget(this);
    QVBoxLayout* layout = new QVBoxLayout(performanceWidget_);

    // 创建标题标签
    QLabel* titleLabel = new QLabel("Class Performance Metrics", performanceWidget_);
    titleLabel->setAlignment(Qt::AlignCenter);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);

    // 创建说明标签
    QLabel* descLabel = new QLabel("Performance metrics for each class", performanceWidget_);
    descLabel->setAlignment(Qt::AlignCenter);
    QFont descFont = descLabel->font();
    descFont.setPointSize(10);
    descLabel->setFont(descFont);

    // 创建类别性能图表
    classPerformancePlot_ = new QCustomPlot(performanceWidget_);
    classPerformancePlot_->setMinimumHeight(400);

    // 创建性能指标表格组件
    metricsTableWidget_ = new QTableWidget(performanceWidget_);

    // 设置表格样式
    metricsTableWidget_->setAlternatingRowColors(true);
    metricsTableWidget_->setSelectionBehavior(QAbstractItemView::SelectRows);
    metricsTableWidget_->setSelectionMode(QAbstractItemView::SingleSelection);
    metricsTableWidget_->setEditTriggers(QAbstractItemView::NoEditTriggers);
    metricsTableWidget_->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    metricsTableWidget_->verticalHeader()->setVisible(false);

    // 设置初始列标题
    metricsTableWidget_->setColumnCount(8);
    metricsTableWidget_->setHorizontalHeaderLabels({
        "Class", "Images", "Instances", "Predictions",
        "Precision", "Recall",
        "mAP50", "mAP50-95"
    });

    // 添加组件到布局
    layout->addWidget(titleLabel);
    layout->addWidget(descLabel);
    layout->addWidget(classPerformancePlot_);
    layout->addWidget(metricsTableWidget_);

    // 添加面板到标签页
    tabWidget_->addTab(performanceWidget_, "Performance");
}

void PerformanceResultsWidget::updatePerformanceCharts(const EvaluationMetrics& metrics)
{
    if (!classPerformancePlot_) {
        return;
    }
    
    createClassPerformanceBarChart(classPerformancePlot_, metrics);
    updateMetricsTable(metrics);
}

void PerformanceResultsWidget::createClassPerformanceBarChart(QCustomPlot* plot, const EvaluationMetrics& metrics)
{
    if (!plot) {
        return;
    }

    // 清除之前的图表内容
    plot->clearPlottables();
    plot->clearItems();

    // 如果没有类别信息，则不显示图表
    if (metrics.perClassMetrics.empty()) {
        return;
    }

    // 创建类别性能指标数据
    QVector<QString> classNames;
    QVector<double> precisionValues;
    QVector<double> recallValues;

    // 使用metrics中的类别顺序，保持与配置文件一致
    // 创建类别名称到指标的映射
    std::map<std::string, const ClassMetrics*> metricsMap;
    for (const auto& classMetric : metrics.perClassMetrics) {
        metricsMap[classMetric.className] = &classMetric;
    }

    // 按照metrics.classNames的顺序提取数据
    for (const auto& className : metrics.classNames) {
        auto it = metricsMap.find(className);
        if (it != metricsMap.end()) {
            classNames.append(QString::fromStdString(className));
            precisionValues.append(it->second->precision);
            recallValues.append(it->second->recall);
        }
    }

    // 创建柱状图 - 使用双柱状图模式
    // 为了清晰区分，我们将每个类别的位置乘以2，这样每个类别有两个位置
    QVector<double> precisionPositions, recallPositions;
    for (int i = 0; i < classNames.size(); ++i) {
        // 每个类别占用2个单位宽度，Precision在左，Recall在右
        precisionPositions << i * 2;
        recallPositions << i * 2 + 1;
    }

    // 创建柱状图
    QCPBars* precisionBars = new QCPBars(plot->xAxis, plot->yAxis);
    QCPBars* recallBars = new QCPBars(plot->xAxis, plot->yAxis);

    // 设置柱状图样式
    precisionBars->setName("Precision");
    precisionBars->setPen(QPen(QColor(54, 162, 235)));
    precisionBars->setBrush(QColor(54, 162, 235, 200));

    recallBars->setName("Recall");
    recallBars->setPen(QPen(QColor(255, 99, 132)));
    recallBars->setBrush(QColor(255, 99, 132, 200));

    // 设置柱状图宽度
    double width = 0.8;  // 柱子宽度
    precisionBars->setWidth(width);
    recallBars->setWidth(width);

    // 设置柱状图数据
    precisionBars->setData(precisionPositions, precisionValues);
    recallBars->setData(recallPositions, recallValues);

    // 设置坐标轴范围
    plot->xAxis->setRange(-1, classNames.size() * 2 + 3);
    plot->yAxis->setRange(0, 1.05); // 性能指标范围为0-1

    // 设置坐标轴标签
    plot->xAxis->setLabel("Class");
    plot->yAxis->setLabel("Value");

    // 设置刻度标签 - 在每对柱子的中间位置显示类别名称
    QSharedPointer<QCPAxisTickerText> textTicker(new QCPAxisTickerText);
    for (int i = 0; i < classNames.size(); ++i) {
        textTicker->addTick(i * 2 + 0.5, classNames[i]);  // 在每对柱子的中间位置
    }
    plot->xAxis->setTicker(textTicker);
    plot->xAxis->setTickLabelRotation(0);  // 设置为0，使标签不倾斜
    plot->xAxis->setSubTicks(false);  // 关闭子刻度

    // 添加图例
    plot->legend->setVisible(true);
    plot->legend->setBrush(QBrush(QColor(255, 255, 255, 200)));
    plot->legend->setBorderPen(QPen(Qt::black));
    plot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop|Qt::AlignRight);

    // 添加网格线
    plot->xAxis->grid()->setVisible(true);
    plot->yAxis->grid()->setVisible(true);
    plot->xAxis->grid()->setSubGridVisible(false);
    plot->yAxis->grid()->setSubGridVisible(false);

    // 添加数值标签
    for (int i = 0; i < classNames.size(); ++i) {
        // 添加 Precision 值标签
        QCPItemText* precisionLabel = new QCPItemText(plot);
        precisionLabel->setPositionAlignment(Qt::AlignTop|Qt::AlignHCenter);
        precisionLabel->position->setCoords(i * 2, precisionValues[i] + 0.03);
        precisionLabel->setText(QString::number(precisionValues[i], 'f', 2));
        precisionLabel->setFont(QFont("Arial", 8));
        precisionLabel->setColor(Qt::black);

        // 添加 Recall 值标签
        QCPItemText* recallLabel = new QCPItemText(plot);
        recallLabel->setPositionAlignment(Qt::AlignTop|Qt::AlignHCenter);
        recallLabel->position->setCoords(i * 2 + 1, recallValues[i] + 0.03);
        recallLabel->setText(QString::number(recallValues[i], 'f', 2));
        recallLabel->setFont(QFont("Arial", 8));
        recallLabel->setColor(Qt::black);
    }

    // 刷新图表
    plot->replot();
}

void PerformanceResultsWidget::createConfusionMatrixTable()
{
    // 创建混淆矩阵容器
    confusionMatrixWidget_ = new QWidget(this);
    QVBoxLayout* layout = new QVBoxLayout(confusionMatrixWidget_);

    // 创建标题标签
    QLabel* titleLabel = new QLabel("Confusion Matrix", confusionMatrixWidget_);
    titleLabel->setAlignment(Qt::AlignCenter);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setMaximumHeight(50);

    // 创建QCustomPlot组件
    confusionMatrixPlot_ = new QCustomPlot(confusionMatrixWidget_);

    // 设置QCustomPlot的基本属性
    confusionMatrixPlot_->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    confusionMatrixPlot_->axisRect()->setupFullAxesBox(true);

    // 设置坐标轴标签
    confusionMatrixPlot_->xAxis->setLabel("True Class");
    confusionMatrixPlot_->yAxis->setLabel("Predicted Class");

    // 设置坐标轴标签字体
    QFont axisFont;
    axisFont.setBold(true);
    axisFont.setPointSize(10);
    confusionMatrixPlot_->xAxis->setLabelFont(axisFont);
    confusionMatrixPlot_->yAxis->setLabelFont(axisFont);

    // 设置坐标轴刻度标签字体
    confusionMatrixPlot_->xAxis->setTickLabelFont(QFont("Arial", 9));
    confusionMatrixPlot_->yAxis->setTickLabelFont(QFont("Arial", 9));

    // 设置最小尺寸
    confusionMatrixPlot_->setMinimumSize(600, 500);

    // 添加组件到布局
    layout->addWidget(titleLabel);
    layout->addWidget(confusionMatrixPlot_);

    // 添加容器到标签页
    tabWidget_->addTab(confusionMatrixWidget_, "Confusion Matrix");
}

void PerformanceResultsWidget::createValidationStatsWidget()
{
    // 创建验证数据统计信息区域
    validationStatsWidget_ = new QWidget(this);
    QVBoxLayout* statsLayout = new QVBoxLayout(validationStatsWidget_);

    // 创建标签
    validationStatsLabel_ = new QLabel(validationStatsWidget_);
    validationStatsLabel_->setTextFormat(Qt::RichText);
    validationStatsLabel_->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    validationStatsLabel_->setWordWrap(true);

    // 设置初始文本
    validationStatsLabel_->setText("<b>Validation:</b> No data available");

    // 添加到布局
    statsLayout->addWidget(validationStatsLabel_);
    validationStatsWidget_->setLayout(statsLayout);
}

void PerformanceResultsWidget::setMetrics(const EvaluationMetrics& metrics)
{
    // 更新验证数据统计信息
    updateValidationStats(metrics);

    // 更新性能总览面板数据
    updateOverviewPanel(metrics);

    // 更新性能指标图表数据
    updatePerformanceCharts(metrics);

    // 更新混淆矩阵表格数据
    updateConfusionMatrixTable(metrics);
}

void PerformanceResultsWidget::updateValidationStats(const EvaluationMetrics& metrics)
{
    // 获取当前时间
    QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");

    // 构建验证数据统计信息
    QString statsText = QString(
        "<b>Validation:</b> %1 images, %2 labels, %3 classes | "
        "mAP<sub>50</sub>: <b>%4</b>, mAP<sub>50-95</sub>: <b>%5</b> | "
        "Precision: <b>%6</b>, Recall: <b>%7</b> | "
        "Time: %8"
    )
    .arg(metrics.totalImages)
    .arg(metrics.totalSamples)
    .arg(metrics.perClassMetrics.size())
    .arg(QString::number(metrics.mAP50, 'f', 4))
    .arg(QString::number(metrics.mAP, 'f', 4))
    .arg(QString::number(metrics.precision, 'f', 4))
    .arg(QString::number(metrics.recall, 'f', 4))
    .arg(currentTime);

    // 设置文本
    validationStatsLabel_->setText(statsText);
}

void PerformanceResultsWidget::updateMetricsTable(const EvaluationMetrics& metrics)
{
    // 清空表格
    metricsTableWidget_->clearContents();

    // 设置行数（类别数量 + 1行总体指标）
    int rowCount = metrics.perClassMetrics.size() + 1;
    metricsTableWidget_->setRowCount(rowCount);

    // 设置列标题 - 参考 Ultralytics YOLO 的输出格式
    metricsTableWidget_->setColumnCount(8);
    metricsTableWidget_->setHorizontalHeaderLabels({
        "Class", "Images", "Instances", "Predictions",
        "Precision", "Recall",
        "mAP50", "mAP50-95"
    });

    // 填充每个类别的指标
    for (int i = 0; i < metrics.perClassMetrics.size(); ++i) {
        const auto& classMetric = metrics.perClassMetrics[i];

        // 类别名称
        QTableWidgetItem* classNameItem = new QTableWidgetItem(QString::fromStdString(classMetric.className));
        metricsTableWidget_->setItem(i, 0, classNameItem);

        // 图像数量 (使用 imageCount 或 0)
        int imageCount = classMetric.imageCount > 0 ? classMetric.imageCount : 0;
        QTableWidgetItem* imageCountItem = new QTableWidgetItem(QString::number(imageCount));
        imageCountItem->setTextAlignment(Qt::AlignCenter);
        metricsTableWidget_->setItem(i, 1, imageCountItem);

        // 样本数量 (实例数)
        QTableWidgetItem* sampleCountItem = new QTableWidgetItem(QString::number(classMetric.sampleCount));
        sampleCountItem->setTextAlignment(Qt::AlignCenter);
        metricsTableWidget_->setItem(i, 2, sampleCountItem);

        // 预测数量
        QTableWidgetItem* predictionCountItem = new QTableWidgetItem(QString::number(classMetric.predictionCount));
        predictionCountItem->setTextAlignment(Qt::AlignCenter);
        metricsTableWidget_->setItem(i, 3, predictionCountItem);

        // 精度
        QTableWidgetItem* precisionItem = new QTableWidgetItem(QString::number(classMetric.precision, 'f', 4));
        precisionItem->setTextAlignment(Qt::AlignCenter);
        metricsTableWidget_->setItem(i, 4, precisionItem);

        // 召回率
        QTableWidgetItem* recallItem = new QTableWidgetItem(QString::number(classMetric.recall, 'f', 4));
        recallItem->setTextAlignment(Qt::AlignCenter);
        metricsTableWidget_->setItem(i, 5, recallItem);

        // mAP50
        QTableWidgetItem* map50Item = new QTableWidgetItem(QString::number(classMetric.ap50, 'f', 4));
        map50Item->setTextAlignment(Qt::AlignCenter);
        metricsTableWidget_->setItem(i, 6, map50Item);

        // mAP50-95
        QTableWidgetItem* mapItem = new QTableWidgetItem(QString::number(classMetric.ap, 'f', 4));
        mapItem->setTextAlignment(Qt::AlignCenter);
        metricsTableWidget_->setItem(i, 7, mapItem);
    }

    // 填充总体指标行
    int lastRow = metrics.perClassMetrics.size();

    // 总体行使用粗体
    QFont boldFont;
    boldFont.setBold(true);

    // 类别名称
    QTableWidgetItem* allClassItem = new QTableWidgetItem("all");
    allClassItem->setFont(boldFont);
    allClassItem->setBackground(QBrush(QColor(240, 240, 240)));
    metricsTableWidget_->setItem(lastRow, 0, allClassItem);

    // 图像总数
    QTableWidgetItem* allImageCountItem = new QTableWidgetItem(QString::number(metrics.totalImages));
    allImageCountItem->setTextAlignment(Qt::AlignCenter);
    allImageCountItem->setFont(boldFont);
    allImageCountItem->setBackground(QBrush(QColor(240, 240, 240)));
    metricsTableWidget_->setItem(lastRow, 1, allImageCountItem);

    // 样本总数
    QTableWidgetItem* allSampleCountItem = new QTableWidgetItem(QString::number(metrics.totalSamples));
    allSampleCountItem->setTextAlignment(Qt::AlignCenter);
    allSampleCountItem->setFont(boldFont);
    allSampleCountItem->setBackground(QBrush(QColor(240, 240, 240)));
    metricsTableWidget_->setItem(lastRow, 2, allSampleCountItem);

    // 预测总数
    QTableWidgetItem* allPredictionCountItem = new QTableWidgetItem(QString::number(metrics.totalPredictions));
    allPredictionCountItem->setTextAlignment(Qt::AlignCenter);
    allPredictionCountItem->setFont(boldFont);
    allPredictionCountItem->setBackground(QBrush(QColor(240, 240, 240)));
    metricsTableWidget_->setItem(lastRow, 3, allPredictionCountItem);

    // 精度
    QTableWidgetItem* allPrecisionItem = new QTableWidgetItem(QString::number(metrics.precision, 'f', 4));
    allPrecisionItem->setTextAlignment(Qt::AlignCenter);
    allPrecisionItem->setFont(boldFont);
    allPrecisionItem->setBackground(QBrush(QColor(240, 240, 240)));
    metricsTableWidget_->setItem(lastRow, 4, allPrecisionItem);

    // 召回率
    QTableWidgetItem* allRecallItem = new QTableWidgetItem(QString::number(metrics.recall, 'f', 4));
    allRecallItem->setTextAlignment(Qt::AlignCenter);
    allRecallItem->setFont(boldFont);
    allRecallItem->setBackground(QBrush(QColor(240, 240, 240)));
    metricsTableWidget_->setItem(lastRow, 5, allRecallItem);

    // mAP50
    QTableWidgetItem* allMap50Item = new QTableWidgetItem(QString::number(metrics.mAP50, 'f', 4));
    allMap50Item->setTextAlignment(Qt::AlignCenter);
    allMap50Item->setFont(boldFont);
    allMap50Item->setBackground(QBrush(QColor(240, 240, 240)));
    metricsTableWidget_->setItem(lastRow, 6, allMap50Item);

    // mAP50-95
    QTableWidgetItem* allMapItem = new QTableWidgetItem(QString::number(metrics.mAP, 'f', 4));
    allMapItem->setTextAlignment(Qt::AlignCenter);
    allMapItem->setFont(boldFont);
    allMapItem->setBackground(QBrush(QColor(240, 240, 240)));
    metricsTableWidget_->setItem(lastRow, 7, allMapItem);

    // 调整表格大小
    metricsTableWidget_->resizeColumnsToContents();
    metricsTableWidget_->resizeRowsToContents();
}

void PerformanceResultsWidget::createOverviewPanel()
{
    // 创建性能总览容器
    overviewWidget_ = new QWidget(this);

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(overviewWidget_);

    // 创建标题标签
    QLabel* titleLabel = new QLabel("Performance Overview", overviewWidget_);
    titleLabel->setAlignment(Qt::AlignCenter);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);

    // 创建网格布局，用于放置图表
    overviewGridLayout_ = new QGridLayout();
    overviewGridLayout_->setSpacing(20); // 设置网格间距

    // 创建类别分布饼图
    QGroupBox* classDistGroupBox = new QGroupBox("Ground Truth Distribution", overviewWidget_);
    QVBoxLayout* classDistLayout = new QVBoxLayout(classDistGroupBox);
    classDistributionPlot_ = new QCustomPlot(classDistGroupBox);
    classDistributionPlot_->setMinimumHeight(250);
    classDistLayout->addWidget(classDistributionPlot_);

    // 创建性能指标条形图
    QGroupBox* perfMetricsGroupBox = new QGroupBox("Performance Metrics", overviewWidget_);
    QVBoxLayout* perfMetricsLayout = new QVBoxLayout(perfMetricsGroupBox);
    performanceBarPlot_ = new QCustomPlot(perfMetricsGroupBox);
    performanceBarPlot_->setMinimumHeight(250);
    perfMetricsLayout->addWidget(performanceBarPlot_);

    // 创建预测统计条形图
    QGroupBox* predStatsGroupBox = new QGroupBox("Prediction Statistics", overviewWidget_);
    QVBoxLayout* predStatsLayout = new QVBoxLayout(predStatsGroupBox);
    predictionStatsPlot_ = new QCustomPlot(predStatsGroupBox);
    predictionStatsPlot_->setMinimumHeight(250);
    predStatsLayout->addWidget(predictionStatsPlot_);

    // 将图表添加到网格布局
    overviewGridLayout_->addWidget(predStatsGroupBox, 0, 0);
    overviewGridLayout_->addWidget(perfMetricsGroupBox, 0, 1);
    overviewGridLayout_->addWidget(classDistGroupBox, 1, 0, 1, 2); // 跨两列

    // 添加组件到主布局
    mainLayout->addWidget(titleLabel);
    mainLayout->addLayout(overviewGridLayout_);
    mainLayout->addStretch(); // 添加弹性空间，使内容居中

    // 添加容器到标签页
    tabWidget_->addTab(overviewWidget_, "Overview");

    // 将Overview标签页设置为默认显示的标签页
    tabWidget_->setCurrentWidget(overviewWidget_);
}

void PerformanceResultsWidget::updateOverviewPanel(const EvaluationMetrics& metrics)
{
    if (!classDistributionPlot_ || !performanceBarPlot_ || !predictionStatsPlot_) {
        return;
    }

    createClassDistributionBarChart(classDistributionPlot_, metrics);
    createPerformanceBarChart(performanceBarPlot_, metrics);
    createPredictionStatsBarChart(predictionStatsPlot_, metrics);
}

void PerformanceResultsWidget::createClassDistributionBarChart(QCustomPlot* plot, const EvaluationMetrics& metrics)
{
    if (!plot) {
        return;
    }

    // 清除之前的图表内容
    plot->clearPlottables();
    plot->clearItems();

    // 计算总样本数，用于计算百分比
    int totalSamples = metrics.totalSamples;
    if (totalSamples <= 0 || metrics.perClassMetrics.empty()) {
        return;
    }

    // 使用metrics中的类别顺序，保持与配置文件一致
    std::vector<std::pair<std::string, int>> classCounts;

    // 创建类别名称到样本数量的映射
    std::map<std::string, int> sampleCountMap;
    for (const auto& classMetric : metrics.perClassMetrics) {
        sampleCountMap[classMetric.className] = classMetric.sampleCount;
    }

    // 按照metrics.classNames的顺序提取数据
    for (const auto& className : metrics.classNames) {
        auto it = sampleCountMap.find(className);
        if (it != sampleCountMap.end()) {
            classCounts.push_back({className, it->second});
        }
    }

    // 创建柱状图数据
    QVector<double> values;
    QVector<QString> labels;
    QVector<QColor> colors;

    // 颜色列表
    QVector<QColor> colorList = {
        QColor(255, 99, 132),   // 红色
        QColor(54, 162, 235),   // 蓝色
        QColor(255, 206, 86),   // 黄色
        QColor(75, 192, 192),   // 青色
        QColor(153, 102, 255),  // 紫色
        QColor(255, 159, 64),   // 橙色
        QColor(199, 199, 199),  // 灰色
        QColor(83, 102, 255),   // 靛蓝色
        QColor(255, 99, 255),   // 粉色
        QColor(99, 255, 132)    // 绿色
    };

    // 添加每个类别的数据
    int colorIndex = 0;
    for (const auto& [className, count] : classCounts) {
        double percentage = 100.0 * count / totalSamples;
        values.append(count);
        labels.append(QString::fromStdString(className));

        // 循环使用颜色
        colors.append(colorList[colorIndex % colorList.size()]);
        colorIndex++;
    }

    // 创建条形图
    QCPBars* bars = new QCPBars(plot->xAxis, plot->yAxis);
    bars->setWidth(0.6);
    bars->setPen(Qt::NoPen);

    // 添加数据
    QVector<double> ticks;
    for (int i = 0; i < labels.size(); ++i) {
        ticks << i;

        // 为每个条形设置不同的颜色
        QCPBars* bar = new QCPBars(plot->xAxis, plot->yAxis);
        bar->setWidth(0.6);
        bar->setPen(Qt::NoPen);

        // 设置渐变填充
        QLinearGradient gradient(0, 0, 0, 1);
        gradient.setCoordinateMode(QGradient::ObjectBoundingMode);
        gradient.setColorAt(0, colors[i]);
        gradient.setColorAt(1, QColor(colors[i].red(), colors[i].green(), colors[i].blue(), 100));
        bar->setBrush(QBrush(gradient));

        // 添加单个数据点
        QVector<double> singleTick, singleValue;
        singleTick << i;
        singleValue << values[i];
        bar->setData(singleTick, singleValue);
    }

    // 设置坐标轴
    plot->xAxis->setRange(-0.5, labels.size() - 0.5);

    // 找出最大值，设置y轴范围
    double maxValue = *std::max_element(values.begin(), values.end());
    plot->yAxis->setRange(0, maxValue * 1.1); // 留出10%的空间

    // 设置坐标轴标签
    plot->xAxis->setLabel("Class");
    plot->yAxis->setLabel("Count");

    // 设置刻度标签
    QSharedPointer<QCPAxisTickerText> textTicker(new QCPAxisTickerText);
    textTicker->addTicks(ticks, labels);
    plot->xAxis->setTicker(textTicker);
    plot->xAxis->setTickLabelRotation(0); // 设置为0，使标签不倾斜

    // 添加数值标签和百分比
    for (int i = 0; i < values.size(); ++i) {
        double percentage = 100.0 * values[i] / totalSamples;

        QCPItemText* textItem = new QCPItemText(plot);
        textItem->setPositionAlignment(Qt::AlignHCenter | Qt::AlignBottom);
        textItem->position->setCoords(i, values[i] + maxValue * 0.03);
        textItem->setText(QString::number(values[i], 'f', 0) + " (" + QString::number(percentage, 'f', 1) + "%)");
        textItem->setFont(QFont("Arial", 8));
    }

    // 添加网格线
    plot->xAxis->grid()->setVisible(true);
    plot->yAxis->grid()->setVisible(true);
    plot->xAxis->grid()->setSubGridVisible(false);
    plot->yAxis->grid()->setSubGridVisible(false);

    // 添加标题
    QCPItemText* titleItem = new QCPItemText(plot);
    titleItem->setPositionAlignment(Qt::AlignHCenter | Qt::AlignTop);
    titleItem->position->setCoords((labels.size() - 1) / 2.0, maxValue * 1.05);
    titleItem->setText("Ground Truth Distribution");
    titleItem->setFont(QFont("Arial", 10, QFont::Bold));

    // 刷新图表
    plot->replot();
}

void PerformanceResultsWidget::createPerformanceBarChart(QCustomPlot* plot, const EvaluationMetrics& metrics)
{
    if (!plot) {
        return;
    }

    // 清除之前的图表内容
    plot->clearPlottables();
    plot->clearItems();

    // 创建性能指标数据
    QVector<QString> names = {"mAP50", "mAP50-95", "Precision", "Recall", "F1-Score"};
    QVector<double> values = {
        metrics.mAP50,
        metrics.mAP,
        metrics.precision,
        metrics.recall,
        metrics.f1Score
    };

    // 创建条形图
    QCPBars* bars = new QCPBars(plot->xAxis, plot->yAxis);
    bars->setWidth(0.6);
    bars->setPen(Qt::NoPen);

    // 设置渐变填充
    QLinearGradient gradient(0, 0, 0, 1);
    gradient.setCoordinateMode(QGradient::ObjectBoundingMode);
    gradient.setColorAt(0, QColor(54, 162, 235));
    gradient.setColorAt(1, QColor(54, 162, 235, 100));
    bars->setBrush(QBrush(gradient));

    // 添加数据
    QVector<double> ticks;
    QVector<QString> labels;
    for (int i = 0; i < names.size(); ++i) {
        ticks << i;
        labels << names[i];
    }

    // 设置坐标轴
    plot->xAxis->setRange(-0.5, names.size() - 0.5);
    double maxValue = *std::max_element(values.begin(), values.end());
    plot->yAxis->setRange(0, maxValue * 1.1); // 性能指标范围为0-1

    // 设置坐标轴标签
    plot->yAxis->setLabel("Value");

    // 设置刻度标签
    QSharedPointer<QCPAxisTickerText> textTicker(new QCPAxisTickerText);
    textTicker->addTicks(ticks, labels);
    plot->xAxis->setTicker(textTicker);
    plot->xAxis->setTickLabelRotation(0); // 设置为0，使标签不倾斜

    // 添加数据
    bars->setData(ticks, values);

    // 添加数值标签
    for (int i = 0; i < values.size(); ++i) {
        QCPItemText* textItem = new QCPItemText(plot);
        textItem->setPositionAlignment(Qt::AlignHCenter | Qt::AlignBottom);
        textItem->position->setCoords(i, values[i] + 0.03);
        textItem->setText(QString::number(values[i], 'f', 3));
        textItem->setFont(QFont("Arial", 8));
    }

    // 添加网格线
    plot->xAxis->grid()->setVisible(true);
    plot->yAxis->grid()->setVisible(true);
    plot->xAxis->grid()->setSubGridVisible(false);
    plot->yAxis->grid()->setSubGridVisible(false);

    // 刷新图表
    plot->replot();
}

void PerformanceResultsWidget::createPredictionStatsBarChart(QCustomPlot* plot, const EvaluationMetrics& metrics)
{
    if (!plot) {
        return;
    }

    // 清除之前的图表内容
    plot->clearPlottables();
    plot->clearItems();

    // 创建预测统计数据
    QVector<QString> names = {"True Positives (TP)", "False Positives (FP)", "False Negatives (FN)"};
    QVector<double> values = {
        static_cast<double>(metrics.TP),
        static_cast<double>(metrics.FP),
        static_cast<double>(metrics.FN)
    };

    // 创建条形图
    QCPBars* bars = new QCPBars(plot->xAxis, plot->yAxis);
    bars->setWidth(0.6);
    bars->setPen(Qt::NoPen);

    // 设置不同颜色
    QVector<QColor> colors = {
        QColor(75, 192, 192),  // 青色 (TP)
        QColor(255, 99, 132),  // 红色 (FP)
        QColor(255, 206, 86)   // 黄色 (FN)
    };

    // 添加数据
    QVector<double> ticks;
    QVector<QString> labels;
    for (int i = 0; i < names.size(); ++i) {
        ticks << i;
        labels << names[i];

        // 为每个条形设置不同的颜色
        QCPBars* bar = new QCPBars(plot->xAxis, plot->yAxis);
        bar->setWidth(0.6);
        bar->setPen(Qt::NoPen);

        // 设置渐变填充
        QLinearGradient gradient(0, 0, 0, 1);
        gradient.setCoordinateMode(QGradient::ObjectBoundingMode);
        gradient.setColorAt(0, colors[i]);
        gradient.setColorAt(1, QColor(colors[i].red(), colors[i].green(), colors[i].blue(), 100));
        bar->setBrush(QBrush(gradient));

        // 添加单个数据点
        QVector<double> singleTick, singleValue;
        singleTick << i;
        singleValue << values[i];
        bar->setData(singleTick, singleValue);
    }

    // 设置坐标轴
    plot->xAxis->setRange(-0.5, names.size() - 0.5);

    // 找出最大值，设置y轴范围
    double maxValue = *std::max_element(values.begin(), values.end());
    plot->yAxis->setRange(0, maxValue * 1.1); // 留出10%的空间

    // 设置坐标轴标签
    plot->yAxis->setLabel("Count");

    // 设置刻度标签
    QSharedPointer<QCPAxisTickerText> textTicker(new QCPAxisTickerText);
    textTicker->addTicks(ticks, labels);
    plot->xAxis->setTicker(textTicker);
    plot->xAxis->setTickLabelRotation(0); // 设置为0，使标签不倾斜

    // 添加数值标签
    for (int i = 0; i < values.size(); ++i) {
        QCPItemText* textItem = new QCPItemText(plot);
        textItem->setPositionAlignment(Qt::AlignHCenter | Qt::AlignBottom);
        textItem->position->setCoords(i, values[i] + maxValue * 0.03);
        textItem->setText(QString::number(values[i], 'f', 0));
        textItem->setFont(QFont("Arial", 8));
    }

    // 添加网格线
    plot->xAxis->grid()->setVisible(true);
    plot->yAxis->grid()->setVisible(true);
    plot->xAxis->grid()->setSubGridVisible(false);
    plot->yAxis->grid()->setSubGridVisible(false);

    // 刷新图表
    plot->replot();
}

void PerformanceResultsWidget::updateConfusionMatrixTable(const EvaluationMetrics& metrics)
{
    // 如果没有类别信息或指针无效，则不显示混淆矩阵
    if (metrics.perClassMetrics.empty() || metrics.confusionMatrix.empty() || !confusionMatrixPlot_) {
        return;
    }

    // 直接使用metrics中的类别名称，保持原始顺序
    std::vector<std::string> classNames = metrics.classNames;

    // 确保包含"background"类别（如果存在于混淆矩阵中）
    bool hasBackground = false;
    for (const auto& row : metrics.confusionMatrix) {
        if (row.first == "background") {
            hasBackground = true;
            break;
        }
        for (const auto& col : row.second) {
            if (col.first == "background") {
                hasBackground = true;
                break;
            }
        }
        if (hasBackground) break;
    }

    if (hasBackground && std::find(classNames.begin(), classNames.end(), "background") == classNames.end()) {
        classNames.push_back("background");
    }

    int numClasses = classNames.size();

    // 清除之前的图表内容
    confusionMatrixPlot_->clearPlottables();
    confusionMatrixPlot_->clearItems();

    // 创建热力图数据
    QCPColorMap* colorMap = new QCPColorMap(confusionMatrixPlot_->xAxis, confusionMatrixPlot_->yAxis);
    colorMap->data()->setSize(numClasses, numClasses);
    colorMap->data()->setRange(QCPRange(-0.5, numClasses - 0.5), QCPRange(-0.5, numClasses - 0.5));

    // 找出混淆矩阵中的最大值，用于颜色映射
    int maxValue = 0;
    for (const auto& row : metrics.confusionMatrix) {
        for (const auto& col : row.second) {
            maxValue = std::max(maxValue, col.second);
        }
    }

    // 填充混淆矩阵数据
    for (int i = 0; i < numClasses; ++i) {
        for (int j = 0; j < numClasses; ++j) {
                    // 获取混淆矩阵中的值
            int value = 0;
            const std::string& trueClass = classNames[i];  // 真实类别(x轴)
            const std::string& predClass = classNames[j];  // 预测类别(y轴)

            // 从混淆矩阵中查找值
            auto it1 = metrics.confusionMatrix.find(trueClass);
            if (it1 != metrics.confusionMatrix.end()) {
                auto it2 = it1->second.find(predClass);
                if (it2 != it1->second.end()) {
                    value = it2->second;
                }
            }

            // 设置热力图数据点 - x是真实类别，y是预测类别
            colorMap->data()->setCell(i, j, value);
        }
    }

    // 设置颜色渐变
    QCPColorGradient gradient;
    gradient.setColorStopAt(0, QColor(255, 255, 255)); // 白色（最小值）
    gradient.setColorStopAt(0.5, QColor(255, 200, 200)); // 浅红色（中间值）
    gradient.setColorStopAt(1, QColor(255, 0, 0)); // 红色（最大值）
    colorMap->setGradient(gradient);

    // 设置数据范围
    colorMap->setDataRange(QCPRange(0, maxValue));

    // 添加颜色条
    QCPColorScale* colorScale = new QCPColorScale(confusionMatrixPlot_);
    confusionMatrixPlot_->plotLayout()->addElement(0, 1, colorScale);
    colorScale->setType(QCPAxis::atRight);
    colorScale->setDataRange(QCPRange(0, maxValue));
    colorScale->setGradient(gradient);
    colorScale->setLabel("Count");
    colorMap->setColorScale(colorScale);

    // 设置坐标轴范围
    confusionMatrixPlot_->xAxis->setRange(-0.5, numClasses - 0.5);
    confusionMatrixPlot_->yAxis->setRange(-0.5, numClasses - 0.5);

    // 设置坐标轴刻度
    // 使用简单的方法设置刻度
    QSharedPointer<QCPAxisTickerText> xTicker(new QCPAxisTickerText);
    QSharedPointer<QCPAxisTickerText> yTicker(new QCPAxisTickerText);

    for (int i = 0; i < numClasses; ++i) {
        xTicker->addTick(i, QString::fromStdString(classNames[i]));
        yTicker->addTick(i, QString::fromStdString(classNames[i]));
    }

    confusionMatrixPlot_->xAxis->setTicker(xTicker);
    confusionMatrixPlot_->yAxis->setTicker(yTicker);

    // 设置坐标轴标签
    confusionMatrixPlot_->xAxis->setLabel("True Class");
    confusionMatrixPlot_->yAxis->setLabel("Predicted Class");

    // 添加数值标签
    for (int i = 0; i < numClasses; ++i) {
        for (int j = 0; j < numClasses; ++j) {
            // 获取混淆矩阵中的值
            int value = 0;
            const std::string& trueClass = classNames[i];  // 真实类别(x轴)
            const std::string& predClass = classNames[j];  // 预测类别(y轴)

            // 从混淆矩阵中查找值
            auto it1 = metrics.confusionMatrix.find(trueClass);
            if (it1 != metrics.confusionMatrix.end()) {
                auto it2 = it1->second.find(predClass);
                if (it2 != it1->second.end()) {
                    value = it2->second;
                }
            }

            // 只为非零值添加标签
            if (value > 0) {
                QCPItemText* textItem = new QCPItemText(confusionMatrixPlot_);
                textItem->setPositionAlignment(Qt::AlignCenter);
                textItem->position->setCoords(i, j); // i=真实类别(x轴)，j=预测类别(y轴)
                textItem->setText(QString::number(value));
                textItem->setFont(QFont("Arial", 9, QFont::Bold));
                textItem->setColor(Qt::black);
            }
        }
    }

    // 刷新图表
    confusionMatrixPlot_->rescaleAxes();
    confusionMatrixPlot_->replot();
}

} // namespace common
} // namespace ui
