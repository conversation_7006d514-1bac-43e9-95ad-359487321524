#include "ui/common/ImageView.h"
#include <QVBoxLayout>
#include <QScrollBar>
#include <opencv2/imgproc.hpp>
#include <QResizeEvent>
#include "utils/Logger.h"
#include "utils/ImageIO.h"

ImageView::ImageView(QWidget *parent) : QWidget(parent) {
    setupUI();
    createConnections();
}

ImageView::~ImageView() {}

void ImageView::setupUI() {
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);

    // 创建标签页控件
    tabWidget_ = new QTabWidget(this);
    tabWidget_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 创建图像标签页
    imageTab_ = new QWidget(this);
    QVBoxLayout* imageLayout = new QVBoxLayout(imageTab_);
    imageLayout->setContentsMargins(0, 0, 0, 0);

    // 图像显示区域使用 QScrollArea
    scrollArea_ = new QScrollArea(imageTab_);
    scrollArea_->setAlignment(Qt::AlignCenter);
    scrollArea_->setWidgetResizable(true);

    imageLabel_ = new QLabel(scrollArea_);
    imageLabel_->setAlignment(Qt::AlignCenter);
    scrollArea_->setWidget(imageLabel_);
    imageLayout->addWidget(scrollArea_);

    // 添加图像标签页
    tabWidget_->addTab(imageTab_, "图像显示");

    // 创建检测结果标签页
    detectionResultsPanel_ = new ui::common::DetectionResultsPanel(this);
    tabWidget_->addTab(detectionResultsPanel_, "检测结果");

    // 创建标注结果标签页
    annotationResultsPanel_ = new ui::common::AnnotationResultsPanel(this);
    tabWidget_->addTab(annotationResultsPanel_, "标注结果");

    // 将标签页控件添加到主布局
    layout->addWidget(tabWidget_);

    // 设置鼠标追踪以支持平移
    setMouseTracking(true);
    imageLabel_->setMouseTracking(true);

    // 安装事件过滤器，确保滚轮事件传递给ImageView
    scrollArea_->installEventFilter(this);
    scrollArea_->viewport()->installEventFilter(this);
    imageLabel_->installEventFilter(this);
}

void ImageView::createConnections() {
}

void ImageView::setImage(const cv::Mat& image, const QString& imagePath) {
    if (!image.empty()) {
        // 保存图像和路径
        originalImage_ = image.clone();
        if (!imagePath.isEmpty()) {
            currentImagePath_ = imagePath;
        }

        cv::Mat rgb;
        cv::cvtColor(image, rgb, cv::COLOR_BGR2RGB);
        QImage qimg(rgb.data, rgb.cols, rgb.rows, rgb.step, QImage::Format_RGB888);

        // 计算适合窗口的初始缩放比例
        QSize viewSize = scrollArea_->viewport()->size();
        double scaleX = static_cast<double>(viewSize.width()) / qimg.width();
        double scaleY = static_cast<double>(viewSize.height()) / qimg.height();
        zoomFactor_ = std::min(scaleX, scaleY);

        // 更新显示
        updateZoom(zoomFactor_, QPoint(width() / 2, height() / 2));

        // 切换到图像标签页
        tabWidget_->setCurrentIndex(0);
    }
}

void ImageView::showPerformanceTab() {
    // 切换到性能指标标签页
    tabWidget_->setCurrentIndex(1);
}

void ImageView::setDetections(const QList<Detection>& detections) {
    // 保存当前检测结果
    currentDetections_ = detections;

    // 如果原始图像有效，则更新检测结果面板
    if (!originalImage_.empty()) {
        // 创建一个深拷贝，确保图像数据不会被释放
        cv::Mat imageCopy = originalImage_.clone();
        detectionResultsPanel_->setDetections(detections, imageCopy, currentImagePath_);
    } else {
        // 尝试从当前图像路径重新加载图像
        if (!currentImagePath_.isEmpty()) {
            cv::Mat img = utils::ImageIO::imageRead(currentImagePath_);
            if (!img.empty()) {
                detectionResultsPanel_->setDetections(detections, img, currentImagePath_);
            }
        }
    }
}

void ImageView::setAnnotations(const QList<Detection>& annotations) {
    // 保存当前标注结果
    currentAnnotations_ = annotations;

    // 如果原始图像有效，则更新标注结果面板
    if (!originalImage_.empty()) {
        // 创建一个深拷贝，确保图像数据不会被释放
        cv::Mat imageCopy = originalImage_.clone();
        annotationResultsPanel_->setAnnotations(annotations, imageCopy, currentImagePath_);
    } else {
        // 尝试从当前图像路径重新加载图像
        if (!currentImagePath_.isEmpty()) {
            cv::Mat img = utils::ImageIO::imageRead(currentImagePath_);
            if (!img.empty()) {
                annotationResultsPanel_->setAnnotations(annotations, img, currentImagePath_);
            }
        }
    }
}

void ImageView::setCurrentAlgorithm(const QString& algorithm) {
    // 保存当前算法类型
    currentAlgorithm_ = algorithm;
    utils::Logger::instance().debug(QString("ImageView: 设置当前算法为 %1").arg(algorithm));

    // 根据算法类型显示或隐藏相关标签页
    // if (algorithm == "WBC" || algorithm == "PLT") {
    //     // 对于YOLO目标检测算法，显示所有标签页
    //     tabWidget_->setTabVisible(1, true); // 性能指标标签页
    //     tabWidget_->setTabVisible(2, true); // 检测结果标签页
    //     tabWidget_->setTabVisible(3, true); // 标注结果标签页
    // } else {
    //     // 对于其他算法，隐藏性能指标和检测结果标签页
    //     tabWidget_->setTabVisible(1, false);
    //     tabWidget_->setTabVisible(2, false);
    //     tabWidget_->setTabVisible(3, false);
    // }

    // 确保当前显示的是图像标签页
    tabWidget_->setCurrentIndex(0);
}

void ImageView::wheelEvent(QWheelEvent* event) {
    if (originalImage_.empty() || tabWidget_->currentIndex() != 0) {
        return;
    }

    // 计算缩放因子
    const double delta = event->angleDelta().y();
    const double factor = 1.0 + (delta / 1200.0);
    const double newZoom = zoomFactor_ * factor;

    // 限制缩放范围
    if (newZoom >= 0.1 && newZoom <= 10.0) {
        // 获取鼠标在scrollArea中的位置
        QPoint mousePos = scrollArea_->mapFromGlobal(event->globalPosition().toPoint());
        zoomAtPoint(newZoom, mousePos);
    }

    event->accept();
}

bool ImageView::eventFilter(QObject* obj, QEvent* event) {
    // 如果是scrollArea或其子控件的滚轮事件，转发给ImageView处理
    if (event->type() == QEvent::Wheel &&
        (obj == scrollArea_ || obj == imageLabel_ || obj == scrollArea_->viewport())) {
        QWheelEvent* wheelEvent = static_cast<QWheelEvent*>(event);

        // 只在图像标签页时处理滚轮事件
        if (tabWidget_->currentIndex() == 0 && !originalImage_.empty()) {
            // 计算缩放因子
            const double delta = wheelEvent->angleDelta().y();
            const double factor = 1.0 + (delta / 1200.0);
            const double newZoom = zoomFactor_ * factor;

            // 限制缩放范围
            if (newZoom >= 0.1 && newZoom <= 10.0) {
                // 获取鼠标在scrollArea中的位置
                QPoint mousePos = scrollArea_->mapFromGlobal(wheelEvent->globalPosition().toPoint());
                zoomAtPoint(newZoom, mousePos);
            }
        }

        return true; // 阻止事件继续传播
    }

    // 其他事件正常处理
    return QWidget::eventFilter(obj, event);
}

void ImageView::mousePressEvent(QMouseEvent* event) {
    if (event->button() == Qt::LeftButton && tabWidget_->currentIndex() == 0) {
        isPanning_ = true;
        lastPanPos_ = event->pos();
        setCursor(Qt::ClosedHandCursor);
    }
}

void ImageView::mouseMoveEvent(QMouseEvent* event) {
    if (isPanning_ && tabWidget_->currentIndex() == 0) {
        QPoint delta = event->pos() - lastPanPos_;
        QScrollBar* hBar = scrollArea_->horizontalScrollBar();
        QScrollBar* vBar = scrollArea_->verticalScrollBar();

        hBar->setValue(hBar->value() - delta.x());
        vBar->setValue(vBar->value() - delta.y());

        lastPanPos_ = event->pos();
    }
}

void ImageView::mouseReleaseEvent(QMouseEvent* event) {
    if (event->button() == Qt::LeftButton) {
        isPanning_ = false;
        setCursor(Qt::ArrowCursor);
    }
}

void ImageView::resizeEvent(QResizeEvent* event) {
    QWidget::resizeEvent(event);

    // 当窗口大小改变时，重新计算图像缩放比例
    if (!originalImage_.empty() && tabWidget_->currentIndex() == 0) {
        cv::Mat rgb;
        cv::cvtColor(originalImage_, rgb, cv::COLOR_BGR2RGB);
        QImage qimg(rgb.data, rgb.cols, rgb.rows, rgb.step, QImage::Format_RGB888);

        QSize viewSize = scrollArea_->viewport()->size();
        double scaleX = static_cast<double>(viewSize.width()) / qimg.width();
        double scaleY = static_cast<double>(viewSize.height()) / qimg.height();
        zoomFactor_ = std::min(scaleX, scaleY);

        updateZoom(zoomFactor_, QPoint(width() / 2, height() / 2));
    }
}

void ImageView::updateZoom(double factor, const QPoint& center) {
    if (originalImage_.empty()) {
        return;
    }

    // 计算缩放后的尺寸
    cv::Mat rgb;
    cv::cvtColor(originalImage_, rgb, cv::COLOR_BGR2RGB);
    QImage qimg(rgb.data, rgb.cols, rgb.rows, rgb.step, QImage::Format_RGB888);

    QSize newSize = qimg.size() * factor;
    QPixmap scaled = QPixmap::fromImage(qimg).scaled(
        newSize,
        Qt::KeepAspectRatio,
        Qt::SmoothTransformation
    );

    // 保存滚动条位置
    QScrollBar* hBar = scrollArea_->horizontalScrollBar();
    QScrollBar* vBar = scrollArea_->verticalScrollBar();

    double hValue = hBar->value() / (double)hBar->maximum();
    double vValue = vBar->value() / (double)vBar->maximum();

    imageLabel_->setPixmap(scaled);

    // 恢复滚动位置
    if (hBar->maximum() > 0) {
        hBar->setValue(int(hBar->maximum() * hValue));
    }
    if (vBar->maximum() > 0) {
        vBar->setValue(int(vBar->maximum() * vValue));
    }
}

void ImageView::zoomAtPoint(double newZoomFactor, const QPoint& centerPoint) {
    if (originalImage_.empty()) {
        return;
    }

    // 获取当前滚动条位置
    QScrollBar* hBar = scrollArea_->horizontalScrollBar();
    QScrollBar* vBar = scrollArea_->verticalScrollBar();

    // 计算鼠标位置相对于图像的坐标（在缩放前）
    QPoint imagePos = centerPoint + QPoint(hBar->value(), vBar->value());

    // 计算缩放前鼠标位置在图像中的相对位置（0.0-1.0）
    QSize currentImageSize = imageLabel_->pixmap().size();
    double relativeX = (double)imagePos.x() / currentImageSize.width();
    double relativeY = (double)imagePos.y() / currentImageSize.height();

    // 更新缩放因子
    zoomFactor_ = newZoomFactor;

    // 重新计算图像尺寸
    cv::Mat rgb;
    cv::cvtColor(originalImage_, rgb, cv::COLOR_BGR2RGB);
    QImage qimg(rgb.data, rgb.cols, rgb.rows, rgb.step, QImage::Format_RGB888);

    QSize newSize = qimg.size() * zoomFactor_;
    QPixmap scaled = QPixmap::fromImage(qimg).scaled(
        newSize,
        Qt::KeepAspectRatio,
        Qt::SmoothTransformation
    );

    imageLabel_->setPixmap(scaled);

    // 计算新的滚动位置，使鼠标位置保持在相同的图像位置
    QSize newImageSize = scaled.size();
    int newImageX = (int)(relativeX * newImageSize.width());
    int newImageY = (int)(relativeY * newImageSize.height());

    // 设置滚动条位置，使鼠标指向的图像位置保持不变
    int newScrollX = newImageX - centerPoint.x();
    int newScrollY = newImageY - centerPoint.y();

    // 确保滚动位置在有效范围内
    newScrollX = std::max(0, std::min(newScrollX, hBar->maximum()));
    newScrollY = std::max(0, std::min(newScrollY, vBar->maximum()));

    hBar->setValue(newScrollX);
    vBar->setValue(newScrollY);
}

cv::Mat ImageView::getCurrentDisplayImage() const {
    // 如果没有原始图像，返回空图像
    if (originalImage_.empty()) {
        return cv::Mat();
    }

    // 直接返回当前显示的图像（已经包含了检测/标注结果）
    return originalImage_.clone();
}