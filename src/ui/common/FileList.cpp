#include "ui/common/FileList.h"
#include <QVBoxLayout>
#include <QFileInfo>

FileList::FileList(QWidget *parent) : QWidget(parent) {
    setFixedHeight(350);
    setupUI();
    createConnections();
}

FileList::~FileList() {}

void FileList::setupUI() {
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);

    listWidget_ = new QListWidget(this);
    listWidget_->setMinimumWidth(200);
    listWidget_->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);

    layout->addWidget(listWidget_);
}

void FileList::createConnections() {
    connect(listWidget_, &QListWidget::currentRowChanged, this, [this](int row) {
        if (row >= 0 && row < fullPathList_.size()) {
            emit currentFileChanged(fullPathList_[row]);
        }
    });
}

void FileList::clear() {
    listWidget_->clear();
    fullPathList_.clear();
    emit fileListChanged(fullPathList_);
}

void FileList::addFiles(const QStringList& files) {
    for (const QString& file : files) {
        QFileInfo fileInfo(file);
        listWidget_->addItem(fileInfo.fileName());
        fullPathList_ << file;
    }
    emit fileListChanged(fullPathList_);
}

void FileList::setCurrentIndex(int index) {
    if (index >= 0 && index < listWidget_->count()) {
        listWidget_->setCurrentRow(index);
    }
}

int FileList::currentIndex() const {
    return listWidget_->currentRow();
}

QString FileList::currentFile() const {
    int index = currentIndex();
    if (index >= 0 && index < fullPathList_.size()) {
        return fullPathList_[index];
    }
    return QString();
}

QStringList FileList::files() const {
    return fullPathList_;
}