#include "AllChannelResultsPanel.h"
#include <QScrollArea>
#include <QFrame>
#include <QSizePolicy>
#include <QSplitter>
#include "utils/Logger.h"

// 样式字符串常量定义
const QString AllChannelResultsPanel::STYLE_SECTION_TITLE = 
    "QLabel { font-weight: bold; color: #2980b9; font-size: 14px; padding: 8px 0px; background-color: #f0f8ff; border-radius: 5px; }";
const QString AllChannelResultsPanel::STYLE_COUNT_SUBTITLE = 
    "QLabel { font-weight: bold; color: #34495e; font-size: 12px; padding: 4px; background-color: #f8f9fa; border-radius: 3px; }";
const QString AllChannelResultsPanel::STYLE_PCT_SUBTITLE = 
    "QLabel { font-weight: bold; color: #34495e; font-size: 12px; padding: 4px; background-color: #fff3e0; border-radius: 3px; }";
const QString AllChannelResultsPanel::STYLE_PARAM_NAME = 
    "QLabel { color: #34495e; font-size: 12px; padding: 2px; font-weight: 500; }";
const QString AllChannelResultsPanel::STYLE_PARAM_VALUE = 
    "QLabel { font-weight: bold; color: #2c3e50; font-size: 12px; padding: 2px 8px; background-color: #e8f5e8; border-radius: 3px; }";
const QString AllChannelResultsPanel::STYLE_PARAM_VALUE_NA = 
    "QLabel { color: #95a5a6; font-style: italic; font-size: 12px; padding: 2px 8px; background-color: #f8f9fa; border-radius: 3px; }";

AllChannelResultsPanel::AllChannelResultsPanel(QWidget *parent)
    : QWidget(parent)
    , mainLayout_(nullptr)
    , scrollArea_(nullptr)
    , scrollWidget_(nullptr)
    , scrollLayout_(nullptr)
    , parametersGroup_(nullptr)
    , parametersLayout_(nullptr)
{
    setupUI();
}

AllChannelResultsPanel::~AllChannelResultsPanel() {
    // Qt会自动清理子对象
}

void AllChannelResultsPanel::setupUI() {
    // 创建主布局
    mainLayout_ = new QVBoxLayout(this);
    mainLayout_->setContentsMargins(10, 10, 10, 10);
    mainLayout_->setSpacing(10);

    // 创建标题
    QLabel* titleLabel = new QLabel("血液学参数统计", this);
    titleLabel->setStyleSheet("QLabel { font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout_->addWidget(titleLabel);

    // 创建滚动区域
    scrollArea_ = new QScrollArea(this);
    scrollArea_->setWidgetResizable(true);
    scrollArea_->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea_->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea_->setFrameShape(QFrame::NoFrame);
    scrollArea_->setStyleSheet("QScrollArea { border: none; background-color: transparent; }");

    // 创建滚动内容容器
    scrollWidget_ = new QWidget();
    scrollArea_->setWidget(scrollWidget_);

    // 创建滚动内容布局
    scrollLayout_ = new QVBoxLayout(scrollWidget_);
    scrollLayout_->setContentsMargins(0, 0, 0, 0);
    scrollLayout_->setSpacing(0);

    // 创建参数显示组
    parametersGroup_ = new QGroupBox("血液学参数", scrollWidget_);
    parametersGroup_->setStyleSheet(
        "QGroupBox { font-weight: bold; font-size: 14px; color: #34495e; border: 2px solid #bdc3c7; border-radius: 8px; margin-top: 10px; padding-top: 10px; }"
        "QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; }"
    );
    parametersGroup_->setFixedWidth(900); // 增加宽度以容纳更好的间距

    // 创建参数布局
    parametersLayout_ = new QGridLayout(parametersGroup_);
    parametersLayout_->setContentsMargins(20, 25, 20, 20);
    parametersLayout_->setVerticalSpacing(10);   // 行间距
    parametersLayout_->setHorizontalSpacing(15); // 列间距

    // 设置列的拉伸比例，在计数和百分比之间增加更多空间
    parametersLayout_->setColumnStretch(0, 2);  // 计数标签列
    parametersLayout_->setColumnStretch(1, 1);  // 计数值列
    parametersLayout_->setColumnStretch(2, 2);  // 百分比标签列
    parametersLayout_->setColumnStretch(3, 1);  // 百分比值列

    // 设置列的最小宽度
    parametersLayout_->setColumnMinimumWidth(0, 180); // 计数标签列
    parametersLayout_->setColumnMinimumWidth(1, 60);  // 计数值列
    parametersLayout_->setColumnMinimumWidth(2, 180); // 百分比标签列
    parametersLayout_->setColumnMinimumWidth(3, 80);  // 百分比值列

    // 添加参数标签
    setupParameterLabels();

    // 将参数组添加到滚动布局
    scrollLayout_->addWidget(parametersGroup_);
    scrollLayout_->addStretch(); // 底部留空间

    // 将滚动区域添加到主布局
    mainLayout_->addWidget(scrollArea_);

    // 初始化参数显示
    clearAllResults();
}

void AllChannelResultsPanel::setupParameterLabels() {
    int row = 0;

    // 白细胞相关参数标题
    QLabel* wbcTitle = new QLabel("白细胞相关参数", parametersGroup_);
    wbcTitle->setStyleSheet(STYLE_SECTION_TITLE);
    wbcTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(wbcTitle, row++, 0, 1, 4);

    // 添加子标题
    QLabel* countTitle = new QLabel("📊 计数", parametersGroup_);
    countTitle->setStyleSheet(STYLE_COUNT_SUBTITLE);
    countTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(countTitle, row, 0, 1, 2);

    QLabel* pctTitle = new QLabel("📈 百分比", parametersGroup_);
    pctTitle->setStyleSheet(STYLE_PCT_SUBTITLE);
    pctTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(pctTitle, row++, 2, 1, 2);

    // 白细胞总数（与计数保持同一列）
    addParameterRow("WBC_COUNT", "白细胞总数 (WBC)", row++, false, false);

    // 白细胞七分类计数和百分比并排
    addParameterRowPair("LYM", "淋巴细胞 (LYM)", "LYM_PCT", "淋巴细胞% (LYM%)", row++);
    addParameterRowPair("MON", "单核细胞 (MON)", "MON_PCT", "单核细胞% (MON%)", row++);
    addParameterRowPair("EOS", "嗜酸性粒细胞 (EOS)", "EOS_PCT", "嗜酸性粒细胞% (EOS%)", row++);
    addParameterRowPair("BAS", "嗜碱性粒细胞 (BAS)", "BAS_PCT", "嗜碱性粒细胞% (BAS%)", row++);
    addParameterRowPair("SEG", "分叶核中性粒细胞 (SEG)", "SEG_PCT", "分叶核中性粒细胞% (SEG%)", row++);
    addParameterRowPair("NSH", "多分叶核中性粒细胞 (NSH)", "NSH_PCT", "多分叶核中性粒细胞% (NSH%)", row++);
    addParameterRowPair("BAND", "杆状核中性粒细胞 (BAND)", "BAND_PCT", "杆状核中性粒细胞% (BAND%)", row++);
    addParameterRowPair("NEU", "中性粒细胞 (NEU)", "NEU_PCT", "中性粒细胞% (NEU%)", row++);

    // 分隔线
    QFrame* line2 = new QFrame(parametersGroup_);
    line2->setFrameShape(QFrame::HLine);
    line2->setFrameShadow(QFrame::Sunken);
    parametersLayout_->addWidget(line2, row++, 0, 1, 4);

    // 红细胞相关参数标题
    QLabel* rbcTitle = new QLabel("红细胞相关参数", parametersGroup_);
    rbcTitle->setStyleSheet(STYLE_SECTION_TITLE);
    rbcTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(rbcTitle, row++, 0, 1, 4);

    // 添加子标题
    QLabel* rbcCountTitle = new QLabel("📊 计数", parametersGroup_);
    rbcCountTitle->setStyleSheet(STYLE_COUNT_SUBTITLE);
    rbcCountTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(rbcCountTitle, row, 0, 1, 2);
    
    QLabel* rbcPctTitle = new QLabel("📈 百分比", parametersGroup_);
    rbcPctTitle->setStyleSheet(STYLE_PCT_SUBTITLE);
    rbcPctTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(rbcPctTitle, row++, 2, 1, 2);
    
    // 红细胞计数（与计数保持同一列）
    addParameterRow("RBC_COUNT", "红细胞计数 (RBC)", row++, false, false);

    // 红细胞分类计数和百分比并排
    addParameterRowPair("SCH", "裂片细胞 (SCH)", "SCH_PCT", "裂片细胞% (SCH%)", row++);
    addParameterRowPair("SPH", "球形红细胞 (SPH)", "SPH_PCT", "球形红细胞% (SPH%)", row++);
    addParameterRowPair("ETG", "影红细胞 (ETG)", "ETG_PCT", "影红细胞% (ETG%)", row++);
    addParameterRowPair("RET", "网织红细胞 (RET)", "RET_PCT", "网织红细胞% (RET%)", row++);
    addParameterRowPair("PRET", "点状网织红细胞 (PRET)", "PRET_PCT", "点状网织红细胞% (PRET%)", row++);
    addParameterRowPair("ARET", "聚集网织红细胞 (ARET)", "ARET_PCT", "聚集网织红细胞% (ARET%)", row++);
    addParameterRowPair("HEI", "海因茨小体 (HEI)", "HEI_PCT", "海因茨小体% (HEI%)", row++);
    addParameterRowPair("MACROCYTE", "大红细胞 (Macrocyte)", "MACROCYTE_PCT", "大红细胞% (Macrocyte%)", row++);
    addParameterRowPair("MICROCYTE", "小红细胞 (Microcyte)", "MICROCYTE_PCT", "小红细胞% (Microcyte%)", row++);
    addParameterRowPair("NRBC", "有核红细胞 (NRBC)", "NRBC_PCT", "有核红细胞% (NRBC%)", row++);
    addParameterRowPair("ECC", "偏心红细胞 (ECC)", "ECC_PCT", "偏心红细胞% (ECC%)", row++);
    addParameterRow("ARBC", "红细胞凝集 (ARBC)", row++, true, false);

    // 红细胞指标（暂未实现，跨列显示）
    addParameterRow("MCV", "平均红细胞体积 (MCV)", row++, true, false);
    addParameterRow("MCH", "平均红细胞血红蛋白含量 (MCH)", row++, true, false);
    addParameterRow("MCHC", "平均红细胞血红蛋白浓度 (MCHC)", row++, true, false);
    addParameterRow("RDW_SD", "红细胞分布宽度标准差 (RDW-SD)", row++, true, false);
    addParameterRow("RDW_CV", "红细胞分布宽度变异系数 (RDW-CV)", row++, true, false);
    addParameterRow("HDW_SD", "血红蛋白分布宽度标准差 (HDW-SD)", row++, true, false);
    addParameterRow("HDW_CV", "血红蛋白分布宽度变异系数 (HDW-CV)", row++, true, false);
    addParameterRow("HSW_CV", "血红蛋白散射宽度变异系数 (HSW-CV)", row++, true, false);
    addParameterRow("HGB", "血红蛋白 (HGB)", row++, true, false);
    addParameterRow("HCT", "红细胞压积 (HCT)", row++, true, false);

    // 分隔线
    QFrame* line3 = new QFrame(parametersGroup_);
    line3->setFrameShape(QFrame::HLine);
    line3->setFrameShadow(QFrame::Sunken);
    parametersLayout_->addWidget(line3, row++, 0, 1, 4);

    // 血小板相关参数标题
    QLabel* pltTitle = new QLabel("血小板相关参数", parametersGroup_);
    pltTitle->setStyleSheet(STYLE_SECTION_TITLE);
    pltTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(pltTitle, row++, 0, 1, 4);

    // 添加子标题
    QLabel* pltCountTitle = new QLabel("📊 计数", parametersGroup_);
    pltCountTitle->setStyleSheet(STYLE_COUNT_SUBTITLE);
    pltCountTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(pltCountTitle, row, 0, 1, 2);
    
    QLabel* pltPctTitle = new QLabel("📈 百分比", parametersGroup_);
    pltPctTitle->setStyleSheet(STYLE_PCT_SUBTITLE);
    pltPctTitle->setAlignment(Qt::AlignCenter);
    parametersLayout_->addWidget(pltPctTitle, row++, 2, 1, 2);
    
    // 血小板计数（与计数保持同一列）
    addParameterRow("PLT_COUNT", "血小板计数 (PLT)", row++, false, false);

    // 血小板分类计数和百分比并排
    addParameterRowPair("LPLT", "大血小板 (LPLT)", "LPLT_PCT", "大血小板% (LPLT%)", row++);
    addParameterRowPair("GPLT", "巨大血小板 (GPLT)", "GPLT_PCT", "巨大血小板% (GPLT%)", row++);
    addParameterRowPair("APLT", "聚集血小板 (APLT)", "APLT_PCT", "聚集血小板% (APLT%)", row++);

    // 血小板指标（暂未实现，跨列显示）
    addParameterRow("MPV", "平均血小板体积 (MPV)", row++, true, false);
    addParameterRow("PDW_SD", "血小板分布宽度标准差 (PDW-SD)", row++, true, false);
    addParameterRow("PDW_CV", "血小板分布宽度变异系数 (PDW-CV)", row++, true, false);
    addParameterRow("PCT", "血小板压积 (PCT)", row++, true, false);
    addParameterRow("PLT_CORRECTED", "血小板校正值 (PLT Corrected)", row++, true, false);
}

void AllChannelResultsPanel::addParameterRow(const QString& key, const QString& name, int row, bool isNotImplemented, bool spanColumns) {
    QLabel* nameLabel = new QLabel(name + ":", parametersGroup_);
    nameLabel->setStyleSheet(STYLE_PARAM_NAME);
    nameLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);

    QLabel* valueLabel = new QLabel(isNotImplemented ? "N/A" : "0", parametersGroup_);
    valueLabel->setStyleSheet(isNotImplemented ? STYLE_PARAM_VALUE_NA : STYLE_PARAM_VALUE);
    valueLabel->setAlignment(Qt::AlignCenter);
    valueLabel->setMinimumWidth(60);

    if (spanColumns) {
        // 跨列显示（用于总计等）
        parametersLayout_->addWidget(nameLabel, row, 0, 1, 2);
        valueLabel->setAlignment(Qt::AlignCenter);
        parametersLayout_->addWidget(valueLabel, row, 2, 1, 2);
    } else {
        // 正常显示
        parametersLayout_->addWidget(nameLabel, row, 0);
        parametersLayout_->addWidget(valueLabel, row, 1);
    }

    parameterLabels_[key] = valueLabel;
}

void AllChannelResultsPanel::addParameterRowPair(const QString& key1, const QString& name1, const QString& key2, const QString& name2, int row) {
    // 左侧：计数
    QLabel* nameLabel1 = new QLabel(name1 + ":", parametersGroup_);
    nameLabel1->setStyleSheet(STYLE_PARAM_NAME);
    nameLabel1->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    parametersLayout_->addWidget(nameLabel1, row, 0);

    QLabel* valueLabel1 = new QLabel("0", parametersGroup_);
    valueLabel1->setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; font-size: 12px; padding: 2px 8px; background-color: #f8f9fa; border-radius: 3px; }");
    valueLabel1->setAlignment(Qt::AlignCenter);
    valueLabel1->setMinimumWidth(50);
    parametersLayout_->addWidget(valueLabel1, row, 1);

    // 右侧：百分比
    QLabel* nameLabel2 = new QLabel(name2 + ":", parametersGroup_);
    nameLabel2->setStyleSheet(STYLE_PARAM_NAME);
    nameLabel2->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    parametersLayout_->addWidget(nameLabel2, row, 2);

    QLabel* valueLabel2 = new QLabel("0.0%", parametersGroup_);
    valueLabel2->setStyleSheet("QLabel { font-weight: bold; color: #e67e22; font-size: 12px; padding: 2px 8px; background-color: #fff3e0; border-radius: 3px; }");
    valueLabel2->setAlignment(Qt::AlignCenter);
    valueLabel2->setMinimumWidth(60);
    parametersLayout_->addWidget(valueLabel2, row, 3);

    // 存储标签引用
    parameterLabels_[key1] = valueLabel1;
    parameterLabels_[key2] = valueLabel2;
}

void AllChannelResultsPanel::updateAlgorithmResults(const QString& algorithm,
                                                   int totalImages,
                                                   int totalDetections,
                                                   double averagePerImage,
                                                   const QMap<QString, int>& labelCounts) {
    // 更新参数数据
    updateParametersFromDetections(algorithm, labelCounts);

    // 计算衍生参数
    calculateDerivedParameters();

    // 更新显示
    updateParametersDisplay();

    utils::Logger::instance().info(QString("更新血液学参数: %1 算法，总图像数: %2，总检测数: %3")
                                  .arg(algorithm).arg(totalImages).arg(totalDetections));
}

void AllChannelResultsPanel::updateParametersFromDetections(const QString& algorithm, const QMap<QString, int>& labelCounts) {
    if (algorithm == "WBC") {
        // 更新白细胞七分类计数
        currentParameters_.LYM = labelCounts.value("LYM", 0);
        currentParameters_.MON = labelCounts.value("MON", 0);
        currentParameters_.EOS = labelCounts.value("EOS", 0);
        currentParameters_.BAS = labelCounts.value("BAS", 0);
        currentParameters_.SEG = labelCounts.value("SEG", 0);
        currentParameters_.NSH = labelCounts.value("NSH", 0);
        currentParameters_.BAND = labelCounts.value("BAND", 0);

        // 计算白细胞总数
        currentParameters_.WBC_COUNT = currentParameters_.LYM + currentParameters_.MON +
                                     currentParameters_.EOS + currentParameters_.BAS +
                                     currentParameters_.SEG + currentParameters_.NSH +
                                     currentParameters_.BAND;

        currentParameters_.hasData = true;
    } else if (algorithm == "RBC") {
        // 更新红细胞计数和分类
        currentParameters_.RBC_COUNT = labelCounts.value("RBC", 0);
        currentParameters_.SCH = labelCounts.value("SCH", 0);
        currentParameters_.SPH = labelCounts.value("SPH", 0);
        currentParameters_.ETG = labelCounts.value("ETG", 0);
        currentParameters_.RET = labelCounts.value("RET", 0);
        currentParameters_.PRET = labelCounts.value("PRET", 0);
        currentParameters_.ARET = labelCounts.value("ARET", 0);
        currentParameters_.HEI = labelCounts.value("HEI", 0);
        currentParameters_.MACROCYTE = labelCounts.value("MACROCYTE", 0);
        currentParameters_.MICROCYTE = labelCounts.value("MICROCYTE", 0);
        currentParameters_.NRBC = labelCounts.value("NRBC", 0);
        currentParameters_.ECC = labelCounts.value("ECC", 0);
        currentParameters_.ARBC = labelCounts.value("ARBC", 0);
        currentParameters_.hasData = true;
    } else if (algorithm == "PLT") {
        // 更新血小板计数和分类
        currentParameters_.PLT_COUNT = labelCounts.value("PLT", 0);
        currentParameters_.LPLT = labelCounts.value("LPLT", 0);
        currentParameters_.GPLT = labelCounts.value("GPLT", 0);
        currentParameters_.APLT = labelCounts.value("APLT", 0);
        currentParameters_.hasData = true;
    }
}

void AllChannelResultsPanel::calculateDerivedParameters() {
    // 计算中性粒细胞总数（杆状+分叶+多分叶）
    currentParameters_.NEU = currentParameters_.BAND + currentParameters_.SEG + currentParameters_.NSH;

    // 计算白细胞七分类百分比
    if (currentParameters_.WBC_COUNT > 0) {
        const double wbcTotal = static_cast<double>(currentParameters_.WBC_COUNT);
        currentParameters_.LYM_PCT = (currentParameters_.LYM / wbcTotal) * 100.0;
        currentParameters_.MON_PCT = (currentParameters_.MON / wbcTotal) * 100.0;
        currentParameters_.EOS_PCT = (currentParameters_.EOS / wbcTotal) * 100.0;
        currentParameters_.BAS_PCT = (currentParameters_.BAS / wbcTotal) * 100.0;
        currentParameters_.SEG_PCT = (currentParameters_.SEG / wbcTotal) * 100.0;
        currentParameters_.NSH_PCT = (currentParameters_.NSH / wbcTotal) * 100.0;
        currentParameters_.BAND_PCT = (currentParameters_.BAND / wbcTotal) * 100.0;
        currentParameters_.NEU_PCT = (currentParameters_.NEU / wbcTotal) * 100.0;
    }

    // 计算红细胞分类百分比
    const int rbcClassificationTotal = currentParameters_.SCH + currentParameters_.SPH +
                                      currentParameters_.ETG + currentParameters_.RET +
                                      currentParameters_.PRET + currentParameters_.ARET +
                                      currentParameters_.HEI + currentParameters_.MACROCYTE +
                                      currentParameters_.MICROCYTE + currentParameters_.NRBC + 
                                      currentParameters_.ECC;

    if (rbcClassificationTotal > 0) {
        const double rbcTotal = static_cast<double>(rbcClassificationTotal);
        currentParameters_.SCH_PCT = (currentParameters_.SCH / rbcTotal) * 100.0;
        currentParameters_.SPH_PCT = (currentParameters_.SPH / rbcTotal) * 100.0;
        currentParameters_.ETG_PCT = (currentParameters_.ETG / rbcTotal) * 100.0;
        currentParameters_.RET_PCT = (currentParameters_.RET / rbcTotal) * 100.0;
        currentParameters_.PRET_PCT = (currentParameters_.PRET / rbcTotal) * 100.0;
        currentParameters_.ARET_PCT = (currentParameters_.ARET / rbcTotal) * 100.0;
        currentParameters_.HEI_PCT = (currentParameters_.HEI / rbcTotal) * 100.0;
        currentParameters_.MACROCYTE_PCT = (currentParameters_.MACROCYTE / rbcTotal) * 100.0;
        currentParameters_.MICROCYTE_PCT = (currentParameters_.MICROCYTE / rbcTotal) * 100.0;
        currentParameters_.NRBC_PCT = (currentParameters_.NRBC / rbcTotal) * 100.0;
        currentParameters_.ECC_PCT = (currentParameters_.ECC / rbcTotal) * 100.0;
    }

    // 计算血小板分类百分比
    const int pltClassificationTotal = currentParameters_.LPLT + 
                                      currentParameters_.GPLT + 
                                      currentParameters_.APLT;

    if (pltClassificationTotal > 0) {
        const double pltTotal = static_cast<double>(pltClassificationTotal);
        currentParameters_.LPLT_PCT = (currentParameters_.LPLT / pltTotal) * 100.0;
        currentParameters_.GPLT_PCT = (currentParameters_.GPLT / pltTotal) * 100.0;
        currentParameters_.APLT_PCT = (currentParameters_.APLT / pltTotal) * 100.0;
    }

    // 其他衍生参数（MCV、MCH、MCHC等）暂未实现，保持为0
}

void AllChannelResultsPanel::updateParametersDisplay() {
    // 更新白细胞七分类计数和总数
    updateIntParameter("LYM", currentParameters_.LYM);
    updateIntParameter("MON", currentParameters_.MON);
    updateIntParameter("EOS", currentParameters_.EOS);
    updateIntParameter("BAS", currentParameters_.BAS);
    updateIntParameter("SEG", currentParameters_.SEG);
    updateIntParameter("NSH", currentParameters_.NSH);
    updateIntParameter("BAND", currentParameters_.BAND);
    updateIntParameter("NEU", currentParameters_.NEU);
    updateIntParameter("WBC_COUNT", currentParameters_.WBC_COUNT);

    // 更新白细胞七分类百分比
    updatePercentageParameter("LYM_PCT", currentParameters_.LYM_PCT);
    updatePercentageParameter("MON_PCT", currentParameters_.MON_PCT);
    updatePercentageParameter("EOS_PCT", currentParameters_.EOS_PCT);
    updatePercentageParameter("BAS_PCT", currentParameters_.BAS_PCT);
    updatePercentageParameter("SEG_PCT", currentParameters_.SEG_PCT);
    updatePercentageParameter("NSH_PCT", currentParameters_.NSH_PCT);
    updatePercentageParameter("BAND_PCT", currentParameters_.BAND_PCT);
    updatePercentageParameter("NEU_PCT", currentParameters_.NEU_PCT);

    // 更新红细胞相关参数
    updateIntParameter("RBC_COUNT", currentParameters_.RBC_COUNT);
    updateIntParameter("SCH", currentParameters_.SCH);
    updateIntParameter("SPH", currentParameters_.SPH);
    updateIntParameter("ETG", currentParameters_.ETG);
    updateIntParameter("RET", currentParameters_.RET);
    updateIntParameter("PRET", currentParameters_.PRET);
    updateIntParameter("ARET", currentParameters_.ARET);
    updateIntParameter("HEI", currentParameters_.HEI);
    updateIntParameter("MACROCYTE", currentParameters_.MACROCYTE);
    updateIntParameter("MICROCYTE", currentParameters_.MICROCYTE);
    updateIntParameter("NRBC", currentParameters_.NRBC);
    updateIntParameter("ECC", currentParameters_.ECC);
    updateIntParameter("ARBC", currentParameters_.ARBC);

    // 更新红细胞分类百分比
    updatePercentageParameter("SCH_PCT", currentParameters_.SCH_PCT);
    updatePercentageParameter("SPH_PCT", currentParameters_.SPH_PCT);
    updatePercentageParameter("ETG_PCT", currentParameters_.ETG_PCT);
    updatePercentageParameter("RET_PCT", currentParameters_.RET_PCT);
    updatePercentageParameter("PRET_PCT", currentParameters_.PRET_PCT);
    updatePercentageParameter("ARET_PCT", currentParameters_.ARET_PCT);
    updatePercentageParameter("HEI_PCT", currentParameters_.HEI_PCT);
    updatePercentageParameter("MACROCYTE_PCT", currentParameters_.MACROCYTE_PCT);
    updatePercentageParameter("MICROCYTE_PCT", currentParameters_.MICROCYTE_PCT);
    updatePercentageParameter("NRBC_PCT", currentParameters_.NRBC_PCT);
    updatePercentageParameter("ECC_PCT", currentParameters_.ECC_PCT);

    // 更新红细胞指标（暂未实现）
    if (parameterLabels_.contains("RDW_SD")) {
        updateDoubleParameter("RDW_SD", currentParameters_.RDW_SD, false, true);
        updateDoubleParameter("RDW_CV", currentParameters_.RDW_CV, false, true);
        updateDoubleParameter("HDW_SD", currentParameters_.HDW_SD, false, true);
        updateDoubleParameter("HDW_CV", currentParameters_.HDW_CV, false, true);
        updateDoubleParameter("HSW_CV", currentParameters_.HSW_CV, false, true);
        updateDoubleParameter("HGB", currentParameters_.HGB, false, true);
        updateDoubleParameter("HCT", currentParameters_.HCT, false, true);
    }

    // 更新血小板相关参数
    updateIntParameter("PLT_COUNT", currentParameters_.PLT_COUNT);
    updateIntParameter("LPLT", currentParameters_.LPLT);
    updateIntParameter("GPLT", currentParameters_.GPLT);
    updateIntParameter("APLT", currentParameters_.APLT);

    // 更新血小板分类百分比
    updatePercentageParameter("LPLT_PCT", currentParameters_.LPLT_PCT);
    updatePercentageParameter("GPLT_PCT", currentParameters_.GPLT_PCT);
    updatePercentageParameter("APLT_PCT", currentParameters_.APLT_PCT);

    // 更新血小板指标（暂未实现）
    if (parameterLabels_.contains("PDW_SD")) {
        updateDoubleParameter("PDW_SD", currentParameters_.PDW_SD, false, true);
        updateDoubleParameter("PDW_CV", currentParameters_.PDW_CV, false, true);
        updateDoubleParameter("PLT_CORRECTED", currentParameters_.PLT_CORRECTED, false, true);
    }
}

void AllChannelResultsPanel::updateIntParameter(const QString& key, int value) {
    if (parameterLabels_.contains(key)) {
        parameterLabels_[key]->setText(QString::number(value));
    }
}

void AllChannelResultsPanel::updateDoubleParameter(const QString& key, double value, bool isPercentage, bool showNA) {
    if (parameterLabels_.contains(key)) {
        QString text = formatParameterValue(value, 1, showNA);
        if (isPercentage) {
            text += "%";
        }
        parameterLabels_[key]->setText(text);
    }
}

void AllChannelResultsPanel::updatePercentageParameter(const QString& key, double value) {
    updateDoubleParameter(key, value, true, false);
}

QString AllChannelResultsPanel::formatParameterValue(double value, int decimals, bool showNA) const {
    if (showNA && value == 0.0) {
        return "N/A";
    }
    return QString::number(value, 'f', decimals);
}

void AllChannelResultsPanel::clearAllResults() {
    // 重置所有参数
    currentParameters_ = HematologyParameters();

    // 更新显示
    updateParametersDisplay();

    utils::Logger::instance().debug("已清零所有血液学参数");
}