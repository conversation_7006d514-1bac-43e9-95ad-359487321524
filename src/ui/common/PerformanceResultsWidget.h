#ifndef PERFORMANCERESULTSWIDGET_H
#define PERFORMANCERESULTSWIDGET_H

#include <QWidget>
#include <QTableWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QHeaderView>
#include <QTabWidget>
#include "qcustomplot.h"
#include "modules/detection/DetectionTypes.h"

namespace ui {
namespace common {

/**
 * @brief 性能结果展示组件
 *
 * 该组件使用QTableWidget展示性能指标和混淆矩阵。
 */
class PerformanceResultsWidget : public QWidget {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父组件
     */
    explicit PerformanceResultsWidget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~PerformanceResultsWidget();

    /**
     * @brief 设置性能指标数据
     * @param metrics 评估指标结构体
     */
    void setMetrics(const EvaluationMetrics& metrics);

private:
    /**
     * @brief 初始化UI组件
     */
    void setupUI();

    /**
     * @brief 创建性能指标表格
     */
    void createMetricsTable();

    /**
     * @brief 创建混淆矩阵表格
     */
    void createConfusionMatrixTable();

    /**
     * @brief 创建验证数据统计信息区域
     */
    void createValidationStatsWidget();

    /**
     * @brief 更新性能指标表格数据
     * @param metrics 评估指标结构体
     */
    void updateMetricsTable(const EvaluationMetrics& metrics);

    /**
     * @brief 更新混淆矩阵表格数据
     * @param metrics 评估指标结构体
     */
    void updateConfusionMatrixTable(const EvaluationMetrics& metrics);

    /**
     * @brief 更新验证数据统计信息
     * @param metrics 评估指标结构体
     */
    void updateValidationStats(const EvaluationMetrics& metrics);

    /**
     * @brief 创建性能总览面板
     */
    void createOverviewPanel();

    /**
     * @brief 更新性能总览面板数据
     * @param metrics 评估指标结构体
     */
    void updateOverviewPanel(const EvaluationMetrics& metrics);

    // 已删除 createPerformanceCharts 方法的声明，因为它是空的

    /**
     * @brief 更新性能指标图表
     * @param metrics 评估指标结构体
     */
    void updatePerformanceCharts(const EvaluationMetrics& metrics);

    /**
     * @brief 创建类别分布柱状图
     * @param plot QCustomPlot对象
     * @param metrics 评估指标结构体
     */
    void createClassDistributionBarChart(QCustomPlot* plot, const EvaluationMetrics& metrics);

    // 已删除 createClassDistributionPieChart 方法的声明，因为它已被 createClassDistributionBarChart 方法替代

    /**
     * @brief 创建性能指标条形图
     * @param plot QCustomPlot对象
     * @param metrics 评估指标结构体
     */
    void createPerformanceBarChart(QCustomPlot* plot, const EvaluationMetrics& metrics);

    /**
     * @brief 创建预测统计条形图
     * @param plot QCustomPlot对象
     * @param metrics 评估指标结构体
     */
    void createPredictionStatsBarChart(QCustomPlot* plot, const EvaluationMetrics& metrics);

    /**
     * @brief 创建每个类别的性能指标条形图
     * @param plot QCustomPlot对象
     * @param metrics 评估指标结构体
     */
    void createClassPerformanceBarChart(QCustomPlot* plot, const EvaluationMetrics& metrics);

    // UI组件
    QTabWidget* tabWidget_;
    QTableWidget* metricsTableWidget_;
    QCustomPlot* confusionMatrixPlot_;  // 混淆矩阵图表
    QWidget* confusionMatrixWidget_;    // 混淆矩阵容器

    QWidget* overviewWidget_;           // 性能总览面板
    QGridLayout* overviewGridLayout_;   // 性能总览网格布局
    QCustomPlot* classDistributionPlot_; // 类别分布柱状图
    QCustomPlot* performanceBarPlot_;    // 性能指标条形图
    QCustomPlot* predictionStatsPlot_;   // 预测统计条形图

    QWidget* performanceWidget_;         // 性能指标面板
    QCustomPlot* classPerformancePlot_;  // 每个类别的性能指标条形图

    QWidget* validationStatsWidget_;
    QLabel* validationStatsLabel_;

    // 布局
    QVBoxLayout* mainLayout_;
};

} // namespace common
} // namespace ui

#endif // PERFORMANCERESULTSWIDGET_H
