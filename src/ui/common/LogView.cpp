#include "ui/common/LogView.h"
#include <QVBoxLayout>
#include <QDateTime>
#include <QPushButton>

LogView::LogView(QWidget *parent) : QWidget(parent) {
    setupUI();
    createConnections();

    // 创建和注册日志回调
    loggerCallback_ = [this](utils::Logger::Level level, const QString& message) {
        onLogMessage(level, message);
    };
    utils::Logger::instance().registerCallback(loggerCallback_);
}

LogView::~LogView() {
    utils::Logger::instance().clearCallback(loggerCallback_);
}

void LogView::setupUI() {
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(0);

    // 创建工具栏
    setupToolBar();
    layout->addWidget(toolBar_);

    // 创建日志组
    groupBox_ = new QGroupBox("日志输出", this);

    // 创建文本编辑器
    textEdit_ = new QTextEdit(groupBox_);
    textEdit_->setReadOnly(true);
    textEdit_->setMinimumHeight(100);

    // 设置日志组布局
    QVBoxLayout *groupLayout = new QVBoxLayout(groupBox_);
    groupLayout->setContentsMargins(10, 10, 10, 10);
    groupLayout->addWidget(textEdit_);

    layout->addWidget(groupBox_);
}

void LogView::setupToolBar() {
    toolBar_ = new QToolBar(this);

    // 保存和清除按钮
    saveAction_ = toolBar_->addAction("保存");
    saveAction_->setToolTip("保存日志到文件");

    clearAction_ = toolBar_->addAction("清除");
    clearAction_->setToolTip("清除所有日志");

    toolBar_->addSeparator();

    // 自动滚动
    autoScrollAction_ = toolBar_->addAction("自动滚动");
    autoScrollAction_->setToolTip("自动滚动到最新日志");
    autoScrollAction_->setCheckable(true);
    autoScrollAction_->setChecked(true);

    toolBar_->addSeparator();

    // 日志级别
    QActionGroup* levelGroup = new QActionGroup(this);
    infoLevelAction_ = toolBar_->addAction("信息");
    debugLevelAction_ = toolBar_->addAction("调试");

    for (auto action : {infoLevelAction_, debugLevelAction_}) {
        action->setCheckable(true);
        action->setToolTip(QString("显示%1级别的日志").arg(action->text()));
        levelGroup->addAction(action);
    }
    infoLevelAction_->setChecked(true);

}

void LogView::createConnections() {
    connect(saveAction_, &QAction::triggered, this, &LogView::saveLog);
    connect(clearAction_, &QAction::triggered, this, &LogView::clearLog);
    connect(autoScrollAction_, &QAction::toggled, this, &LogView::toggleAutoScroll);
    connect(debugLevelAction_, &QAction::triggered, this, [this]() { setLogLevel(utils::Logger::Level::Debug); });
    connect(infoLevelAction_, &QAction::triggered, this, [this]() { setLogLevel(utils::Logger::Level::Info); });
}

void LogView::saveLog() {
    QString fileName = QFileDialog::getSaveFileName(this,
        "保存日志",
        QString("%1/log_%2.txt")
            .arg(QDir::currentPath())
            .arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        "文本文件 (*.txt);;所有文件 (*.*)");

    if (fileName.isEmpty()) {
        return;
    }

    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream(&file) << textEdit_->toPlainText();
        utils::Logger::instance().info("日志已保存到: " + fileName);
    } else {
        utils::Logger::instance().error("保存日志失败: " + fileName);
    }
}

void LogView::clearLog() {
    textEdit_->clear();
    utils::Logger::instance().info("日志已清除");
}

void LogView::toggleAutoScroll(bool enabled) {
    autoScroll_ = enabled;
    if (enabled && !textEdit_->toPlainText().isEmpty()) {
        QTextCursor cursor = textEdit_->textCursor();
        cursor.movePosition(QTextCursor::End);
        textEdit_->setTextCursor(cursor);
    }
    utils::Logger::instance().info(QString("自动滚动已%1").arg(enabled ? "开启" : "关闭"));
}

void LogView::setLogLevel(utils::Logger::Level level) {
    currentLogLevel_ = level;
    utils::Logger::instance().setLogLevel(level);

    // 更新UI状态
    debugLevelAction_->setChecked(level == utils::Logger::Level::Debug);
    infoLevelAction_->setChecked(level == utils::Logger::Level::Info);

    // 清空当前文本并重新应用过滤
    textEdit_->clear();

    // 输出日志级别变更信息
    QString levelStr;
    switch (level) {
        case utils::Logger::Level::Debug: levelStr = "调试"; break;
        case utils::Logger::Level::Info: levelStr = "信息"; break;
        case utils::Logger::Level::Warning: levelStr = "警告"; break;
        case utils::Logger::Level::Error: levelStr = "错误"; break;
    }
    utils::Logger::instance().info(QString("日志级别已设置为: %1").arg(levelStr));
}

void LogView::clear() {
    textEdit_->clear();
}

void LogView::setMaximumHeight(int height) {
    textEdit_->setMaximumHeight(height);
}

void LogView::append(const QString& text) {
    textEdit_->append(text);
    if (autoScroll_) {
        textEdit_->verticalScrollBar()->setValue(textEdit_->verticalScrollBar()->maximum());
    }
}

void LogView::onLogMessage(utils::Logger::Level level, const QString& message) {
    // 只显示当前级别的日志
    if (level < currentLogLevel_) {
        return;
    }

    // 在UI线程中更新文本
    QMetaObject::invokeMethod(this, [this, level, message]() {
        QTextCharFormat format;

        // 设置不同级别的颜色
        switch (level) {
            case utils::Logger::Level::Debug:
                format.setForeground(QColor("#808080")); // 灰色
                break;
            case utils::Logger::Level::Info:
                // format.setForeground(QColor("#000000")); // 黑色
                break;
            case utils::Logger::Level::Warning:
                format.setForeground(QColor("#FF8C00")); // 深橙色
                break;
            case utils::Logger::Level::Error:
                format.setForeground(QColor("#FF0000")); // 红色
                break;
        }

        // 插入带格式的文本
        QTextCursor cursor = textEdit_->textCursor();
        cursor.movePosition(QTextCursor::End);
        cursor.mergeCharFormat(format);
        cursor.insertText(message);
        cursor.insertBlock();

        if (autoScroll_) {
            textEdit_->verticalScrollBar()->setValue(textEdit_->verticalScrollBar()->maximum());
        }
    }, Qt::QueuedConnection);
}