#ifndef DETECTIONRESULTSPANEL_H
#define DETECTIONRESULTSPANEL_H

#include <QWidget>
#include <QScrollArea>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QGroupBox>
#include <QMap>
#include <QList>
#include <opencv2/core.hpp>
#include "modules/detection/DetectionTypes.h"

namespace ui {
namespace common {

class DetectionResultsPanel : public QWidget {
    Q_OBJECT
public:
    explicit DetectionResultsPanel(QWidget *parent = nullptr);
    ~DetectionResultsPanel();

    // 设置检测结果
    void setDetections(const QList<Detection>& detections, const cv::Mat& sourceImage, const QString& imagePath = "");

    // 清除所有检测结果
    void clearResults();

private:
    void setupUI();

    // 裁切检测目标并显示
    void displayDetections();

    // 创建类别分组
    QGroupBox* createCategoryGroup(const QString& category);

    // 添加裁切图像到类别分组
    void addCroppedImageToCategory(const QString& category, const QPixmap& pixmap, const QString& label, const QString& sourceImageName = "", float confidence = 0.0f);

    // 主布局
    QVBoxLayout* mainLayout_;

    // 滚动区域
    QScrollArea* scrollArea_;
    QWidget* scrollContent_;
    QVBoxLayout* contentLayout_;

    // 存储类别分组
    QMap<QString, QGroupBox*> categoryGroups_;
    QMap<QString, QHBoxLayout*> categoryLayouts_;

    // 存储检测结果和源图像
    QList<Detection> detections_;
    cv::Mat sourceImage_;
    QString currentImagePath_; // 当前图像路径

    // 裁切图像的最大尺寸
    const int MAX_CROP_SIZE = 150;
};

} // namespace common
} // namespace ui

#endif // DETECTIONRESULTSPANEL_H
