#ifndef ALLCHANNELRESULTSPANEL_H
#define ALLCHANNELRESULTSPANEL_H

#include <QWidget>
#include <QLabel>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFrame>
#include <QScrollArea>
#include <QMap>
#include <QString>

/**
 * @brief 血液学参数统计面板
 *
 * 显示血液学分析的关键参数，包括：
 * - 白细胞七分类计数和百分比 (LYM, MON, EOS, BAS, NEU等)
 * - 红细胞相关参数 (RBC计数, MCV, MCH, MCHC, RDW-SD/CV, HDW-SD/CV, HGB, HCT等)
 * - 血小板相关参数 (PLT计数, MPV, PDW-SD/CV, PCT等)
 *
 * 特性：
 * - 初始状态所有参数显示为0或N/A
 * - 批量预测完成后根据检测结果计算相应参数
 * - 切换算法不影响参数显示
 * - 只有更新保存路径时才清零所有参数
 * - 为未实现的参数（如MCV、MCHC等）预留接口
 */
class AllChannelResultsPanel : public QWidget
{
    Q_OBJECT

public:
    explicit AllChannelResultsPanel(QWidget *parent = nullptr);
    ~AllChannelResultsPanel();

    /**
     * @brief 更新指定算法的检测结果并计算血液学参数
     * @param algorithm 算法名称 ("WBC", "PLT", "RBC")
     * @param totalImages 总图像数量
     * @param totalDetections 总检测数量
     * @param averagePerImage 平均每张图像的检测数量
     * @param labelCounts 各标签的检测数量统计
     */
    void updateAlgorithmResults(const QString& algorithm,
                               int totalImages,
                               int totalDetections,
                               double averagePerImage,
                               const QMap<QString, int>& labelCounts);

    /**
     * @brief 清零所有血液学参数
     * 通常在更新保存路径（加载新样本）时调用
     */
    void clearAllResults();

private:
    // 血液学参数结构
    struct HematologyParameters {
        // 白细胞相关参数
        int WBC_COUNT = 0;      // 白细胞计数 (White Blood Cell Count)
        int LYM = 0;    // 淋巴细胞 (Lymphocyte)
        int MON = 0;    // 单核细胞 (Monocyte)
        int EOS = 0;    // 嗜酸性粒细胞 (Eosinophil)
        int BAS = 0;    // 嗜碱性粒细胞 (Basophil)
        int SEG = 0;    // 分叶核中性粒细胞 (Segmented neutrophil)
        int NSH = 0;   // 多分叶核中性粒细胞 (Multi-segmented neutrophil)
        int BAND = 0;   // 杆状核中性粒细胞 (Band neutrophil)
        int NEU = 0;    // 中性粒细胞总数 (Neutrophil Total) = BAND + SEG + NSH

        // 白细胞分类百分比
        double LYM_PCT = 0.0;
        double MON_PCT = 0.0;
        double EOS_PCT = 0.0;
        double BAS_PCT = 0.0;
        double SEG_PCT = 0.0;
        double NSH_PCT = 0.0;
        double BAND_PCT = 0.0;
        double NEU_PCT = 0.0;  // 中性粒细胞百分比

        // 红细胞相关参数
        int RBC_COUNT = 0;      // 红细胞计数 (Red Blood Cell Count)
        int SCH = 0;    // 裂片细胞 (SCH)
        int SPH = 0;     // 球形红细胞 (SPH)
        int ETG = 0;     // 影红细胞 (ETG)
        int RET = 0;   // 网织红细胞 (RET)
        int PRET = 0;   // 点状网织红细胞 (Punctate RET)
        int ARET = 0;  // 聚集网织红细胞 (Aggregate RET)
        int HEI = 0;     // 海因茨小体 (HEI)
        int MACROCYTE = 0;      // 大红细胞 (Macrocyte)
        int MICROCYTE = 0;      // 小红细胞 (Microcyte)
        int NRBC = 0;           // 有核红细胞 (Nucleated RBC)
        int ECC = 0;  // 偏心红细胞 (ECC)
        int ARBC = 0; // 红细胞凝集 (ARBC)

        // 红细胞分类百分比
        double SCH_PCT = 0.0;    // 裂片细胞% (SCH%)
        double SPH_PCT = 0.0;     // 球形红细胞% (SPH%)
        double ETG_PCT = 0.0;     // 影红细胞% (ETG%)
        double RET_PCT = 0.0;   // 网织红细胞% (RET%)
        double PRET_PCT = 0.0;   // 点状网织红细胞% (PRET%)
        double ARET_PCT = 0.0;  // 聚集网织红细胞% (ARET%)
        double HEI_PCT = 0.0;     // 海因茨小体% (HEI%)
        double MACROCYTE_PCT = 0.0;      // 大红细胞% (Macrocyte%)
        double MICROCYTE_PCT = 0.0;      // 小红细胞% (Microcyte%)
        double NRBC_PCT = 0.0;           // 有核红细胞% (NRBC%)
        double ECC_PCT = 0.0;  // 偏心红细胞% (ECC%)

        double MCV = 0.0;       // 平均红细胞体积 (Mean Corpuscular Volume) (暂未实现)
        double MCH = 0.0;       // 平均红细胞血红蛋白含量 (Mean Corpuscular Hemoglobin) (暂未实现)
        double MCHC = 0.0;      // 平均红细胞血红蛋白浓度 (Mean Corpuscular Hemoglobin Concentration) (暂未实现)
        double RDW_SD = 0.0;    // 红细胞分布宽度标准差 (RDW-SD) (暂未实现)
        double RDW_CV = 0.0;    // 红细胞分布宽度变异系数 (RDW-CV) (暂未实现)
        double HDW_SD = 0.0;    // 血红蛋白分布宽度标准差 (HDW-SD) (暂未实现)
        double HDW_CV = 0.0;    // 血红蛋白分布宽度变异系数 (HDW-CV) (暂未实现)
        double HSW_CV = 0.0;    // 血红蛋白散射宽度变异系数 (HSW-CV) (暂未实现)
        double HGB = 0.0;       // 血红蛋白 (Hemoglobin) (暂未实现)
        double HCT = 0.0;       // 红细胞压积 (Hematocrit) (暂未实现)

        // 血小板相关参数
        int PLT_COUNT = 0;      // 血小板计数 (Platelet Count)
        int LPLT = 0;      // 大血小板 (Large Platelet)
        int GPLT = 0;      // 巨大血小板 (Giant Platelet)
        int APLT = 0;  // 聚集血小板 (Aggregate Platelet)

        // 血小板分类百分比
        double LPLT_PCT = 0.0;      // 大血小板% (LPLT%)
        double GPLT_PCT = 0.0;      // 巨大血小板% (GPLT%)
        double APLT_PCT = 0.0;  // 聚集血小板% (APLT%)

        double MPV = 0.0;       // 平均血小板体积 (Mean Platelet Volume) (暂未实现)
        double PDW_SD = 0.0;    // 血小板分布宽度标准差 (PDW-SD) (暂未实现)
        double PDW_CV = 0.0;    // 血小板分布宽度变异系数 (PDW-CV) (暂未实现)
        double PCT = 0.0;       // 血小板压积 (Plateletcrit) (暂未实现)
        double PLT_CORRECTED = 0.0; // 血小板校正值 (Platelet Corrected Value) (暂未实现)

        bool hasData = false;
    };

    void setupUI();
    void setupParameterLabels();
    void addParameterRow(const QString& key, const QString& name, int row, bool isNotImplemented = false, bool spanColumns = false);
    void addParameterRowPair(const QString& key1, const QString& name1, const QString& key2, const QString& name2, int row);
    void updateParametersFromDetections(const QString& algorithm, const QMap<QString, int>& labelCounts);
    void updateParametersDisplay();
    void calculateDerivedParameters();
    QString formatParameterValue(double value, int decimals = 1, bool showNA = false) const;

    // 辅助方法：批量更新显示
    void updateIntParameter(const QString& key, int value);
    void updateDoubleParameter(const QString& key, double value, bool isPercentage = false, bool showNA = false);
    void updatePercentageParameter(const QString& key, double value);

    // 样式字符串常量
    static const QString STYLE_SECTION_TITLE;
    static const QString STYLE_COUNT_SUBTITLE;
    static const QString STYLE_PCT_SUBTITLE;
    static const QString STYLE_PARAM_NAME;
    static const QString STYLE_PARAM_VALUE;
    static const QString STYLE_PARAM_VALUE_NA;

    // UI组件
    QVBoxLayout* mainLayout_;
    QScrollArea* scrollArea_;
    QWidget* scrollWidget_;
    QVBoxLayout* scrollLayout_;
    QGroupBox* parametersGroup_;
    QGridLayout* parametersLayout_;

    // 数据存储
    HematologyParameters currentParameters_;

    // 参数显示标签
    QMap<QString, QLabel*> parameterLabels_;
};

#endif // ALLCHANNELRESULTSPANEL_H
