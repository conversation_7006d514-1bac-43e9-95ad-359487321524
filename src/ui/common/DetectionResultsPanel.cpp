#include "ui/common/DetectionResultsPanel.h"
#include "utils/Logger.h"
#include "utils/ImageIO.h"
#include "utils/ConfigManager.h"
#include <QPixmap>
#include <QImage>
#include <QFileInfo>
#include <opencv2/imgproc.hpp>
#include <algorithm>

namespace ui {
namespace common {

DetectionResultsPanel::DetectionResultsPanel(QWidget *parent) : QWidget(parent) {
    setupUI();
}

DetectionResultsPanel::~DetectionResultsPanel() {
}

void DetectionResultsPanel::setupUI() {
    // 创建主布局
    mainLayout_ = new QVBoxLayout(this);
    mainLayout_->setContentsMargins(0, 0, 0, 0);
    mainLayout_->setSpacing(10);

    // 创建标题标签
    QLabel* titleLabel = new QLabel("检测结果分类", this);
    titleLabel->setAlignment(Qt::AlignCenter);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);

    // 创建说明标签
    QLabel* descLabel = new QLabel("按类别显示所有检测到的目标", this);
    descLabel->setAlignment(Qt::AlignCenter);
    QFont descFont = descLabel->font();
    descFont.setPointSize(10);
    descLabel->setFont(descFont);

    // 创建滚动区域
    scrollArea_ = new QScrollArea(this);
    scrollArea_->setWidgetResizable(true);
    scrollArea_->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea_->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 创建滚动内容
    scrollContent_ = new QWidget(scrollArea_);
    contentLayout_ = new QVBoxLayout(scrollContent_);
    contentLayout_->setAlignment(Qt::AlignTop);
    contentLayout_->setSpacing(20);
    scrollContent_->setLayout(contentLayout_);

    scrollArea_->setWidget(scrollContent_);

    // 添加组件到主布局
    mainLayout_->addWidget(titleLabel);
    mainLayout_->addWidget(descLabel);
    mainLayout_->addWidget(scrollArea_);
}

void DetectionResultsPanel::setDetections(const QList<Detection>& detections, const cv::Mat& sourceImage, const QString& imagePath) {
    // 保存检测结果和图像路径
    detections_ = detections;
    currentImagePath_ = imagePath;

    // 如果传入的是已经绘制了检测框的图像，需要重新读取原始图像
    if (!imagePath.isEmpty()) {
        sourceImage_ = utils::ImageIO::imageRead(imagePath);
        if (sourceImage_.empty()) {
            // 如果无法读取原始图像，才使用传入的图像
            sourceImage_ = sourceImage.clone();
        }
    } else if (!sourceImage.empty()) {
        sourceImage_ = sourceImage.clone();
    }

    // 显示检测结果
    displayDetections();
}

void DetectionResultsPanel::clearResults() {
    // 清除所有类别分组
    for (auto it = categoryGroups_.begin(); it != categoryGroups_.end(); ++it) {
        delete it.value();
    }

    categoryGroups_.clear();
    categoryLayouts_.clear();
}

void DetectionResultsPanel::displayDetections() {
    // 清除之前的结果
    clearResults();

    // 检查源图像是否有效
    if (sourceImage_.empty() || detections_.isEmpty()) {
        return;
    }

    // 获取每个类别的最大显示数量
    int maxDisplayPerCategory = utils::ConfigManager::instance().getMaxDisplayPerCategory();

    // 按类别分组检测结果，并限制每个类别的显示数量
    QMap<QString, QList<Detection>> categoryDetections;
    QMap<QString, int> categoryTotalCounts; // 记录每个类别的总数量

    for (const auto& detection : detections_) {
        QString category = QString::fromStdString(detection.label);
        categoryTotalCounts[category]++;

        // 添加到临时列表中进行排序
        categoryDetections[category].append(detection);
    }

    // 对每个类别的检测结果按置信度排序（从高到低），然后限制显示数量
    for (auto it = categoryDetections.begin(); it != categoryDetections.end(); ++it) {
        QList<Detection>& detections = it.value();

        // 按置信度从高到低排序
        std::sort(detections.begin(), detections.end(), [](const Detection& a, const Detection& b) {
            return a.confidence > b.confidence;
        });

        // 限制显示数量
        if (detections.size() > maxDisplayPerCategory) {
            detections = detections.mid(0, maxDisplayPerCategory);
        }
    }

    // 创建图像缓存，避免重复加载相同的图像
    QMap<QString, cv::Mat> imageCache;

    // 按类别分组显示检测结果
    for (auto it = categoryDetections.begin(); it != categoryDetections.end(); ++it) {
        const QString& category = it.key();
        const QList<Detection>& detectionsToShow = it.value();
        int totalCount = categoryTotalCounts[category];

        // 为每个类别创建分组
        createCategoryGroup(category);

        // 更新类别分组标题，显示总数和当前显示数
        QString groupTitle;
        if (totalCount > maxDisplayPerCategory) {
            groupTitle = QString("%1 (显示 %2/%3)").arg(category).arg(detectionsToShow.size()).arg(totalCount);
        } else {
            groupTitle = QString("%1 (%2)").arg(category).arg(totalCount);
        }
        categoryGroups_[category]->setTitle(groupTitle);

        // 处理该类别中要显示的检测结果
        for (const auto& detection : detectionsToShow) {

        // 获取要裁切的图像
        cv::Mat imageToProcess;

        // 如果检测结果包含图像路径，则从该路径加载图像
        if (!detection.imagePath.empty()) {
            QString imagePath = QString::fromStdString(detection.imagePath);

            // 检查图像是否已经在缓存中
            if (imageCache.contains(imagePath)) {
                imageToProcess = imageCache[imagePath];
            } else {
                imageToProcess = utils::ImageIO::imageRead(imagePath);
                if (imageToProcess.empty()) {
                    utils::Logger::instance().warning(QString("无法加载图像: %1").arg(imagePath));
                    continue;
                }

                // 将图像添加到缓存
                imageCache[imagePath] = imageToProcess.clone();
            }
        } else {
            // 使用当前源图像
            if (sourceImage_.empty()) {
                utils::Logger::instance().warning("源图像为空，无法裁切");
                continue;
            }
            imageToProcess = sourceImage_;
        }

        // 裁切目标区域
        cv::Rect bbox = detection.bbox;

        // 确保边界框在图像范围内
        bbox.x = std::max(0, bbox.x);
        bbox.y = std::max(0, bbox.y);

        // 检查边界框是否超出图像边界
        if (bbox.x >= imageToProcess.cols || bbox.y >= imageToProcess.rows) {
            utils::Logger::instance().warning(QString("边界框位置超出图像范围 - 类别: %1, 位置: (%2, %3, %4, %5)")
                .arg(QString::fromStdString(detection.label))
                .arg(bbox.x).arg(bbox.y).arg(bbox.width).arg(bbox.height));
            continue;
        }

        // 调整宽度和高度以确保不超出图像边界
        bbox.width = std::min(bbox.width, imageToProcess.cols - bbox.x);
        bbox.height = std::min(bbox.height, imageToProcess.rows - bbox.y);

        // 检查边界框是否有效
        if (bbox.width <= 0 || bbox.height <= 0) {
            utils::Logger::instance().warning(QString("忽略无效边界框 - 类别: %1, 位置: (%2, %3, %4, %5)")
                .arg(QString::fromStdString(detection.label))
                .arg(bbox.x).arg(bbox.y).arg(bbox.width).arg(bbox.height));
            continue;
        }

        QPixmap pixmap;

        try {
            // 裁切图像
            cv::Mat cropped = imageToProcess(bbox).clone();

            // 计算缩放比例，确保裁切图像不超过最大尺寸
            double scale = 1.0;
            if (cropped.cols > MAX_CROP_SIZE || cropped.rows > MAX_CROP_SIZE) {
                double scaleX = static_cast<double>(MAX_CROP_SIZE) / cropped.cols;
                double scaleY = static_cast<double>(MAX_CROP_SIZE) / cropped.rows;
                scale = std::min(scaleX, scaleY);
            }

            // 确保缩放比例有效
            if (scale <= 0 || std::isnan(scale) || std::isinf(scale)) {
                scale = 1.0;
                utils::Logger::instance().warning("检测到无效的缩放比例，重置为1.0");
            }

            cv::Mat resized;
            cv::resize(cropped, resized, cv::Size(), scale, scale, cv::INTER_AREA);

            // 转换为QPixmap
            cv::Mat rgb;
            cv::cvtColor(resized, rgb, cv::COLOR_BGR2RGB);
            QImage qimg(rgb.data, rgb.cols, rgb.rows, rgb.step, QImage::Format_RGB888);
            pixmap = QPixmap::fromImage(qimg);

            if (pixmap.isNull()) {
                continue;
            }

        } catch (const cv::Exception& e) {
            utils::Logger::instance().error(QString("OpenCV异常: %1").arg(e.what()));
            continue;
        } catch (const std::exception& e) {
            utils::Logger::instance().error(QString("标准异常: %1").arg(e.what()));
            continue;
        } catch (...) {
            utils::Logger::instance().error("未知异常");
            continue;
        }

        // 创建标签文本
        QString labelText;
        QString sourceImageName;

        if (!detection.imagePath.empty()) {
            // 如果有图像路径，获取文件名
            QFileInfo fileInfo(QString::fromStdString(detection.imagePath));
            sourceImageName = fileInfo.fileName();
            labelText = QString("%1 (%2)").arg(category).arg(QString::number(detection.confidence, 'f', 2));
        } else {
            labelText = QString("%1 (%2)").arg(category).arg(QString::number(detection.confidence, 'f', 2));
        }

            // 添加到对应类别分组
            addCroppedImageToCategory(category, pixmap, labelText, sourceImageName, detection.confidence);
        }
    }

    // 更新UI
    scrollContent_->updateGeometry();
    scrollArea_->updateGeometry();
}

QGroupBox* DetectionResultsPanel::createCategoryGroup(const QString& category) {
    // 检查类别分组是否已存在
    if (categoryGroups_.contains(category)) {
        return categoryGroups_[category];
    }

    // 创建类别分组
    QGroupBox* groupBox = new QGroupBox(scrollContent_);

    // 设置类别名称（标题将在displayDetections中设置）
    groupBox->setTitle(category);

    // 创建垂直布局作为主布局
    QVBoxLayout* groupLayout = new QVBoxLayout(groupBox);
    groupLayout->setContentsMargins(10, 15, 10, 10);
    groupLayout->setSpacing(10);

    // 创建水平布局用于放置裁切图像
    QHBoxLayout* imagesLayout = new QHBoxLayout();
    imagesLayout->setSpacing(30); // 设置固定间距
    imagesLayout->setContentsMargins(5, 10, 5, 5);
    imagesLayout->setAlignment(Qt::AlignLeft | Qt::AlignTop); // 靠左上对齐

    // 添加弹性空间，防止单个类别时布局被拉伸
    imagesLayout->addStretch();

    // 添加流式布局到主布局
    groupLayout->addLayout(imagesLayout);

    // 保存类别分组和布局
    categoryGroups_[category] = groupBox;
    categoryLayouts_[category] = imagesLayout;

    // 添加到内容布局
    contentLayout_->addWidget(groupBox);

    return groupBox;
}

void DetectionResultsPanel::addCroppedImageToCategory(const QString& category, const QPixmap& pixmap, const QString& label, const QString& sourceImageName, float confidence) {
    // 确保类别分组存在
    if (!categoryGroups_.contains(category)) {
        createCategoryGroup(category);
    }

    // 检查pixmap是否有效
    if (pixmap.isNull()) {
        return;
    }

    // 创建图像容器
    QWidget* imageContainer = new QWidget();
    QVBoxLayout* containerLayout = new QVBoxLayout(imageContainer);
    containerLayout->setContentsMargins(5, 5, 5, 5);
    containerLayout->setSpacing(5);
    containerLayout->setAlignment(Qt::AlignTop | Qt::AlignHCenter); // 设置顶部居中对齐

    // 设置容器的固定宽度，确保有足够的空间显示文件名
    int containerWidth = std::max(200, pixmap.width() + 40);
    imageContainer->setFixedWidth(containerWidth);

    // 设置容器的最大高度，防止过度拉伸
    imageContainer->setMaximumHeight(pixmap.height() + 100);

    // 创建图像标签
    QLabel* imageLabel = new QLabel();
    imageLabel->setPixmap(pixmap);
    imageLabel->setAlignment(Qt::AlignCenter);
    imageLabel->setFixedSize(pixmap.width() + 10, pixmap.height() + 10);
    imageLabel->setFrameShape(QFrame::Box);
    imageLabel->setFrameShadow(QFrame::Sunken);

    // 移除这个弹性空间，因为我们已经改为顶部对齐
    // QSpacerItem* verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);
    // containerLayout->addItem(verticalSpacer);

    // 设置图像标签的样式
    imageLabel->setStyleSheet("border: 1px solid #cccccc; border-radius: 4px; background-color: #f9f9f9;");

    // 创建置信度标签
    QLabel* confLabel = new QLabel(QString("置信度: %1").arg(QString::number(confidence, 'f', 2)));
    confLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    confLabel->setWordWrap(true);
    confLabel->setMinimumWidth(containerWidth - 10); // 确保文本标签有足够的宽度
    confLabel->setContentsMargins(5, 0, 0, 0); // 添加左边距，使文本与图像边缘对齐

    // 设置置信度标签的样式
    QFont confFont = confLabel->font();
    confFont.setPointSize(9); // 设置字体大小
    confFont.setItalic(false); // 不使用斜体
    confLabel->setFont(confFont);
    // confLabel->setStyleSheet("color: #000000;"); // 设置为黑色

    // 创建来源图像名标签
    QLabel* sourceLabel = nullptr;
    if (!sourceImageName.isEmpty()) {
        sourceLabel = new QLabel(QString("%1").arg(sourceImageName));
        sourceLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        sourceLabel->setWordWrap(true);
        sourceLabel->setMinimumWidth(containerWidth - 10); // 确保文本标签有足够的宽度
        sourceLabel->setContentsMargins(5, 0, 0, 0); // 添加左边距，使文本与图像边缘对齐

        // 设置来源标签的样式
        QFont sourceFont = sourceLabel->font();
        sourceFont.setPointSize(9); // 设置字体大小稍小
        sourceFont.setItalic(false); // 不使用斜体
        sourceLabel->setFont(sourceFont);
        // sourceLabel->setStyleSheet("color: #000000;"); // 设置为黑色
    }

    // 添加到容器 - 按顺序添加图像、置信度、文件名
    containerLayout->addWidget(imageLabel);
    containerLayout->addWidget(confLabel);

    if (sourceLabel) {
        containerLayout->addWidget(sourceLabel);
    }

    // 添加弹性空间，防止容器被过度拉伸
    containerLayout->addStretch();

    // 设置容器的样式
    imageContainer->setStyleSheet("background-color: transparent;");

    // 添加到类别布局（使用水平布局）
    QHBoxLayout* hboxLayout = categoryLayouts_[category];

    // 在添加新的图像容器之前，先移除之前添加的弹性空间
    if (hboxLayout->count() > 0) {
        QLayoutItem* lastItem = hboxLayout->itemAt(hboxLayout->count() - 1);
        if (lastItem && lastItem->spacerItem()) {
            hboxLayout->removeItem(lastItem);
            delete lastItem;
        }
    }

    hboxLayout->addWidget(imageContainer);

    // 重新添加弹性空间到末尾，保持左对齐
    hboxLayout->addStretch();

    // 标题现在在displayDetections中统一设置，不在这里更新
}

} // namespace common
} // namespace ui
