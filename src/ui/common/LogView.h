#ifndef LOGVIEW_H
#define LOGVIEW_H

#include <QWidget>
#include <QTextEdit>
#include <QGroupBox>
#include <QToolBar>
#include <QAction>
#include <QActionGroup>
#include <QFileDialog>
#include <QScrollBar>
#include <QDir>
#include "utils/Logger.h"

class LogView : public QWidget {
    Q_OBJECT
public:
    explicit LogView(QWidget *parent = nullptr);
    ~LogView();

    // 获取控件
    QTextEdit* textEdit() const { return textEdit_; }

    // 日志操作
    void clear();
    void setMaximumHeight(int height);
    void append(const QString& text);

private slots:
    void saveLog();
    void clearLog();
    void toggleAutoScroll(bool enabled);
    void setLogLevel(utils::Logger::Level level);

private:
    void setupUI();
    void createConnections();
    void setupToolBar();
    void onLogMessage(utils::Logger::Level level, const QString& message);

    QGroupBox* groupBox_;
    QTextEdit* textEdit_;
    QToolBar* toolBar_;
    utils::Logger::LogCallback loggerCallback_;
    bool autoScroll_{true};
    utils::Logger::Level currentLogLevel_{utils::Logger::Level::Info}; // 添加当前日志级别

    // 工具栏动作
    QAction* saveAction_;
    QAction* clearAction_;
    QAction* autoScrollAction_;
    QAction* debugLevelAction_;
    QAction* infoLevelAction_;
    QAction* warningLevelAction_;
    QAction* errorLevelAction_;
};

#endif // LOGVIEW_H