#ifndef RBC_PANEL_H
#define RBC_PANEL_H

#include <QObject>
#include <QWidget>
#include <QGroupBox>
#include <QPushButton>
#include <QLineEdit>
#include <QDoubleSpinBox>
#include <QSpinBox>
#include <QFormLayout>
#include <QLabel>
#include <QString>
#include <QStringList>
#include <QDir>
#include <QFutureWatcher>
#include <QRadioButton>
#include <QButtonGroup>
#include <QCheckBox>
#include <opencv2/core/core.hpp>
#include "modules/detection/YoloAlgorithm.h" // 包含EvaluationMetrics结构


// 前向声明
namespace cv {
    class Mat;
}

namespace ui {
namespace RBC {

class RBCPanel : public QWidget {
    Q_OBJECT

public:
    // 构造函数和析构函数
    explicit RBCPanel(QWidget *parent = nullptr);
    ~RBCPanel();

    // 获取参数
    float confidenceThreshold() const;
    float nmsThreshold() const;
    QString modelPath() const;

signals:
    // 参数变化信号
    void parametersChanged();
    
    // 图像处理相关信号
    void processedImageReady(const cv::Mat& result);
    
    // 验证相关信号
    void validationCompleted(const EvaluationMetrics& metrics);
    
    // 结果相关信号
    void detectionsReady(const QList<Detection>& detections);
    void annotationsReady(const QList<Detection>& annotations);
    
    // 进度相关信号
    void batchProcessStarted(int total);
    void batchProcessProgress(int current, int total);
    void batchProcessFinished();

    // 批量预测结果统计信号
    void batchStatisticsReady(const QString& algorithm,
                             int totalImages,
                             int totalDetections,
                             double averagePerImage,
                             const QMap<QString, int>& labelCounts);

public slots:
    // 参数设置
    void setConfidenceThreshold(float value);
    void setNmsThreshold(float value);
    void setModelPath(const QString& path);
    
    // 图像相关
    void setCurrentImage(const QString& path);
    void setImagePaths(const QStringList& paths) { imagePaths_ = paths; }
    
    // 数据清理
    void clearResults();      // 清除预测结果
    void clearAnnotations(); // 清除标注数据
    
    // 进度更新
    Q_INVOKABLE void updateBatchProgress(int current, int total) {
        emit batchProcessProgress(current, total);
    }

private slots:
    // 操作按钮响应
    void onLoadModelClicked();
    void onPredictClicked();
    void onPredictBatchClicked();

    // 验证操作按钮响应
    void onLoadGTClicked();
    void onValidateClicked();
    void onCropAnnotationsClicked();  // 新增：裁切标注按钮响应

    // 异步操作完成回调
    void onPredictFutureFinished();
    void onBatchPredictFutureFinished();
    void onValidateFutureFinished();

private:
    // 私有工具函数
    void setupUI();
    void createConnections();
    bool loadDefaultModel();

    // UI 组件 - 基础设置
    QGroupBox* modelGroup_;
    QGroupBox* parametersGroup_;
    QGroupBox* operationGroup_;
    QGroupBox* validationGroup_;       // 新增：验证操作组
    QLineEdit* modelPathEdit_;
    QPushButton* btnLoadModel_;

    // UI 组件 - 操作按钮
    QPushButton* btnPredict_;
    QPushButton* btnPredictBatch_;

    // UI 组件 - 验证操作按钮
    QPushButton* btnLoadGT_;
    QPushButton* btnValidate_;
    QPushButton* btnCropAnnotations_;  // 新增：裁切标注按钮

    // UI 组件 - 参数设置
    QDoubleSpinBox* confidenceSpinBox_;
    QDoubleSpinBox* nmsThresholdSpinBox_;
    QCheckBox* filterBoundaryTargetsCheckBox_;  // 剔除边界目标复选框
    QSpinBox* boundaryThresholdSpinBox_;        // 边界阈值参数
    QCheckBox* saveResultsCheckBox_;
    QCheckBox* saveTxtResultsCheckBox_;
    QCheckBox* saveJsonResultsCheckBox_;
    QCheckBox* useFixedSizeCheckBox_;
    QSpinBox* fixedWidthSpinBox_;
    QSpinBox* fixedHeightSpinBox_;

    // 数据成员
    QString currentImagePath_;
    QStringList imagePaths_;
    EvaluationMetrics lastValidationMetrics_;
    std::shared_ptr<YoloAlgorithm> yoloAlgorithm_;

    // 异步操作观察器
    QFutureWatcher<bool>* predictWatcher_;
    QFutureWatcher<bool>* batchPredictWatcher_;
    QFutureWatcher<EvaluationMetrics>* validateWatcher_;
};

} // namespace RBC
} // namespace ui

#endif // RBC_PANEL_H