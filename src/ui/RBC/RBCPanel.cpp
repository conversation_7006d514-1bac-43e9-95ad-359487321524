#include "ui/RBC/RBCPanel.h"
#include "modules/detection/YoloAlgorithm.h"
#include "utils/Logger.h"
#include "utils/ImageIO.h"
#include "utils/ConfigManager.h"
#include <QFileDialog>
#include <QFuture>
#include <QFutureWatcher>
#include <QtConcurrent>
#include <QRadioButton>
#include <QButtonGroup>
#include <opencv2/imgproc.hpp>
#include <algorithm>
#include <iomanip>
#include <sstream>

namespace ui {
namespace RBC {

// 构造函数与基础设置

RBCPanel::RBCPanel(QWidget *parent) : QWidget(parent) {
    // 初始化 YOLO Algorithm 实例，设置为RBC类型
    yoloAlgorithm_ = std::make_shared<YoloAlgorithm>(AlgorithmType::RBC);

    setupUI();

    // 从配置初始化参数
    yoloAlgorithm_->setConfidenceThreshold(utils::ConfigManager::instance().getConfidenceThreshold());
    yoloAlgorithm_->setNmsThreshold(utils::ConfigManager::instance().getNmsThreshold());
    yoloAlgorithm_->setSaveResults(utils::ConfigManager::instance().getSaveResults());
    yoloAlgorithm_->setSaveTxtResults(utils::ConfigManager::instance().getSaveTxtResults());
    yoloAlgorithm_->setUseFixedSize(utils::ConfigManager::instance().getUseFixedSize());
    yoloAlgorithm_->setFixedSize(
        utils::ConfigManager::instance().getFixedWidth(),
        utils::ConfigManager::instance().getFixedHeight()
    );
    yoloAlgorithm_->setFilterBoundaryTargets(utils::ConfigManager::instance().getFilterBoundaryTargets());
    yoloAlgorithm_->setBoundaryThreshold(utils::ConfigManager::instance().getBoundaryThreshold());

    createConnections();

    // 初始化 Future Watchers
    predictWatcher_ = new QFutureWatcher<bool>(this);
    batchPredictWatcher_ = new QFutureWatcher<bool>(this);
    validateWatcher_ = new QFutureWatcher<EvaluationMetrics>(this);

    connect(predictWatcher_, &QFutureWatcher<bool>::finished,
            this, &RBCPanel::onPredictFutureFinished);
    connect(batchPredictWatcher_, &QFutureWatcher<bool>::finished,
            this, &RBCPanel::onBatchPredictFutureFinished);
    connect(validateWatcher_, &QFutureWatcher<EvaluationMetrics>::finished,
            this, &RBCPanel::onValidateFutureFinished);

    // 尝试加载默认模型
    loadDefaultModel();
}

RBCPanel::~RBCPanel() {
}

bool RBCPanel::loadDefaultModel() {
    // 从配置获取默认模型路径
    QString defaultModelPath = utils::ConfigManager::instance().getRBCModelPath();
    QFileInfo modelFile(defaultModelPath);

    // 检查默认模型是否存在
    if (!modelFile.exists()) {
        utils::Logger::instance().info("默认模型不存在: " + defaultModelPath);
        return false;
    }

    // 更新UI显示
    modelPathEdit_->setText(defaultModelPath);

    // 加载模型
    if (yoloAlgorithm_->loadModel(defaultModelPath.toStdString())) {
        utils::Logger::instance().info("默认模型加载成功: " + defaultModelPath);

        // 加载类别名称
        yoloAlgorithm_->loadClassNames(utils::ConfigManager::instance().getRBCClasses());
        return true;
    } else {
        utils::Logger::instance().warning("默认模型加载失败: " + defaultModelPath);
        return false;
    }
}

// UI 布局与连接

void RBCPanel::setupUI() {
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(10);

    // 创建模型组
    modelGroup_ = new QGroupBox("模型设置", this);
    QFormLayout* modelLayout = new QFormLayout(modelGroup_);

    modelPathEdit_ = new QLineEdit(this);
    modelPathEdit_->setReadOnly(true);
    btnLoadModel_ = new QPushButton(QIcon(":/icons/load_model.svg"), "加载模型", this);

    QHBoxLayout* modelPathLayout = new QHBoxLayout();
    modelPathLayout->addWidget(modelPathEdit_, 1);
    modelPathLayout->addWidget(btnLoadModel_);

    modelLayout->addRow("模型路径:", modelPathLayout);

    // 创建参数组
    parametersGroup_ = new QGroupBox("算法参数", this);
    QVBoxLayout* paramsVLayout = new QVBoxLayout(parametersGroup_);

    // 创建置信度和NMS阈值的水平布局
    QHBoxLayout* thresholdsLayout = new QHBoxLayout();

    // 创建置信度部分
    QVBoxLayout* confidenceLayout = new QVBoxLayout();
    QLabel* confidenceLabel = new QLabel("置信度阈值:", this);
    confidenceSpinBox_ = new QDoubleSpinBox(this);
    confidenceSpinBox_->setRange(0.01, 1.0);
    confidenceSpinBox_->setSingleStep(0.01);
    confidenceSpinBox_->setValue(utils::ConfigManager::instance().getConfidenceThreshold());
    confidenceLayout->addWidget(confidenceLabel);
    confidenceLayout->addWidget(confidenceSpinBox_);

    // 创建NMS阈值部分
    QVBoxLayout* nmsLayout = new QVBoxLayout();
    QLabel* nmsLabel = new QLabel("NMS阈值:", this);
    nmsThresholdSpinBox_ = new QDoubleSpinBox(this);
    nmsThresholdSpinBox_->setRange(0.01, 1.0);
    nmsThresholdSpinBox_->setSingleStep(0.01);
    nmsThresholdSpinBox_->setValue(utils::ConfigManager::instance().getNmsThreshold());
    nmsLayout->addWidget(nmsLabel);
    nmsLayout->addWidget(nmsThresholdSpinBox_);

    // 将两个参数布局添加到水平布局中
    thresholdsLayout->addLayout(confidenceLayout);
    thresholdsLayout->addLayout(nmsLayout);

    // 创建边界过滤设置
    QHBoxLayout* boundaryFilterLayout = new QHBoxLayout();
    filterBoundaryTargetsCheckBox_ = new QCheckBox("剔除边界目标", this);
    filterBoundaryTargetsCheckBox_->setChecked(utils::ConfigManager::instance().getFilterBoundaryTargets());
    filterBoundaryTargetsCheckBox_->setToolTip("勾选后，算法预测结果将剔除距离图像边界太近的目标");

    // 创建边界阈值部分布局（包含标签和SpinBox）
    QHBoxLayout* boundaryThresholdLayout = new QHBoxLayout();
    QLabel* boundaryThresholdLabel = new QLabel("边界距离:", this);
    boundaryThresholdSpinBox_ = new QSpinBox(this);
    boundaryThresholdSpinBox_->setRange(1, 100);
    boundaryThresholdSpinBox_->setSuffix(" 像素");
    boundaryThresholdSpinBox_->setValue(utils::ConfigManager::instance().getBoundaryThreshold());
    boundaryThresholdSpinBox_->setEnabled(filterBoundaryTargetsCheckBox_->isChecked());
    boundaryThresholdSpinBox_->setToolTip("目标中心点距离图像边界的最小距离");
    
    boundaryThresholdLayout->addWidget(boundaryThresholdLabel);
    boundaryThresholdLayout->addWidget(boundaryThresholdSpinBox_);

    boundaryFilterLayout->addWidget(filterBoundaryTargetsCheckBox_);
    boundaryFilterLayout->addLayout(boundaryThresholdLayout);

    // 将布局添加到参数组（包含核心检测参数和边界过滤）
    paramsVLayout->addLayout(thresholdsLayout);
    paramsVLayout->addLayout(boundaryFilterLayout);

    // 创建其他选项组
    QGroupBox* otherParametersGroup = new QGroupBox("其他选项", this);
    QVBoxLayout* otherParamsVLayout = new QVBoxLayout(otherParametersGroup);

    // 创建保存结果复选框和布局
	QHBoxLayout* checkboxesLayout = new QHBoxLayout();
    saveTxtResultsCheckBox_ = new QCheckBox("保存检测结果(txt)", this);
    saveTxtResultsCheckBox_->setChecked(utils::ConfigManager::instance().getSaveTxtResults());
    saveJsonResultsCheckBox_ = new QCheckBox("保存检测结果(json)", this);
    saveJsonResultsCheckBox_->setChecked(utils::ConfigManager::instance().getSaveTxtResults());
    checkboxesLayout->addWidget(saveTxtResultsCheckBox_);
    checkboxesLayout->addWidget(saveJsonResultsCheckBox_);

    // 创建裁切结果设置
    saveResultsCheckBox_ = new QCheckBox("保存裁切结果", this);
    saveResultsCheckBox_->setChecked(utils::ConfigManager::instance().getSaveResults());
    useFixedSizeCheckBox_ = new QCheckBox("固定宽高裁切", this);
    useFixedSizeCheckBox_->setChecked(utils::ConfigManager::instance().getUseFixedSize());
    QHBoxLayout* resultsCropLayout = new QHBoxLayout();
    resultsCropLayout->addWidget(saveResultsCheckBox_);
    resultsCropLayout->addWidget(useFixedSizeCheckBox_);

    QHBoxLayout* fixedSizeLayout = new QHBoxLayout();
    QLabel* widthLabel = new QLabel("宽度:", this);
    fixedWidthSpinBox_ = new QSpinBox(this);
    fixedWidthSpinBox_->setRange(1, 1000);
    fixedWidthSpinBox_->setValue(utils::ConfigManager::instance().getFixedWidth());
    fixedWidthSpinBox_->setEnabled(false);

    QLabel* heightLabel = new QLabel("高度:", this);
    fixedHeightSpinBox_ = new QSpinBox(this);
    fixedHeightSpinBox_->setRange(1, 1000);
    fixedHeightSpinBox_->setValue(utils::ConfigManager::instance().getFixedHeight());
    fixedHeightSpinBox_->setEnabled(false);

    QLabel* cropHintLabel = new QLabel("(固定宽高裁切时使用)", this);
    fixedSizeLayout->addWidget(widthLabel);
    fixedSizeLayout->addWidget(fixedWidthSpinBox_);
    fixedSizeLayout->addWidget(heightLabel);
    fixedSizeLayout->addWidget(fixedHeightSpinBox_);
    fixedSizeLayout->addWidget(cropHintLabel);
    fixedSizeLayout->addStretch();

    // 将保存相关参数添加到其他选项组
    otherParamsVLayout->addLayout(checkboxesLayout);

    // 添加第一条分隔线
    QFrame* line1 = new QFrame(this);
    line1->setFrameShape(QFrame::HLine);
    line1->setFrameShadow(QFrame::Sunken);
    otherParamsVLayout->addWidget(line1);

    otherParamsVLayout->addLayout(resultsCropLayout);
    otherParamsVLayout->addLayout(fixedSizeLayout);

    // 创建操作组
    operationGroup_ = new QGroupBox("预测操作", this);
    QGridLayout* opLayout = new QGridLayout(operationGroup_);

    // 创建带图标的操作按钮
    btnPredict_ = new QPushButton(QIcon(":/icons/run.svg"), "单次预测", this);
    btnPredictBatch_ = new QPushButton(QIcon(":/icons/run_batch.svg"), "批量预测", this);

    // 添加按钮到网格布局
    opLayout->addWidget(btnPredict_, 0, 0);
    opLayout->addWidget(btnPredictBatch_, 0, 1);
    opLayout->setSpacing(10);

    // 创建验证操作组
    validationGroup_ = new QGroupBox("验证操作", this);
    QGridLayout* validationLayout = new QGridLayout(validationGroup_);

    // 创建验证操作按钮
    btnLoadGT_ = new QPushButton(QIcon(":/icons/load_label.svg"), "加载标注", this);
    btnValidate_ = new QPushButton(QIcon(":/icons/validate.svg"), "验证结果", this);
    btnCropAnnotations_ = new QPushButton(QIcon(":/icons/crop.svg"), "裁切标注", this);

    // 添加验证按钮到网格布局
    validationLayout->addWidget(btnLoadGT_, 0, 0);
    validationLayout->addWidget(btnCropAnnotations_, 0, 1);
    validationLayout->addWidget(btnValidate_, 1, 0, 1, 2);  // 跨两列
    validationLayout->setSpacing(10);

    // 将所有分组添加到主布局
    mainLayout->addWidget(modelGroup_);
    mainLayout->addWidget(parametersGroup_);
    mainLayout->addWidget(otherParametersGroup);
    mainLayout->addWidget(operationGroup_);
    mainLayout->addWidget(validationGroup_);
    mainLayout->addStretch();
}

void RBCPanel::createConnections() {
    // 模型和预测相关连接
    connect(btnLoadModel_, &QPushButton::clicked, this, &RBCPanel::onLoadModelClicked);
    connect(btnPredict_, &QPushButton::clicked, this, &RBCPanel::onPredictClicked);
    connect(btnPredictBatch_, &QPushButton::clicked, this, &RBCPanel::onPredictBatchClicked);

    // 验证操作相关连接
    connect(btnLoadGT_, &QPushButton::clicked, this, &RBCPanel::onLoadGTClicked);
    connect(btnValidate_, &QPushButton::clicked, this, &RBCPanel::onValidateClicked);
    connect(btnCropAnnotations_, &QPushButton::clicked, this, &RBCPanel::onCropAnnotationsClicked);

    // 参数变化连接
    connect(confidenceSpinBox_, QOverload<double>::of(&QDoubleSpinBox::valueChanged), [this](double value) {
        if (yoloAlgorithm_) {
            yoloAlgorithm_->setConfidenceThreshold(static_cast<float>(value));
        }
        emit parametersChanged();
    });

    connect(nmsThresholdSpinBox_, QOverload<double>::of(&QDoubleSpinBox::valueChanged), [this](double value) {
        if (yoloAlgorithm_) {
            yoloAlgorithm_->setNmsThreshold(static_cast<float>(value));
        }
        emit parametersChanged();
    });

    // 保存设置连接
    connect(saveResultsCheckBox_, &QCheckBox::stateChanged, [this](int state) {
        if (yoloAlgorithm_) {
            bool value = state == Qt::Checked;
            yoloAlgorithm_->setSaveResults(value);
            utils::Logger::instance().debug(QString("目标裁切结果保存设置已更新: %1").arg(value ? "保存" : "不保存"));
        }
        emit parametersChanged();
    });

    connect(saveTxtResultsCheckBox_, &QCheckBox::stateChanged, [this](int state) {
        if (yoloAlgorithm_) {
            bool value = state == Qt::Checked;
            yoloAlgorithm_->setSaveTxtResults(value);
            utils::Logger::instance().debug(QString("TXT检测结果保存设置已更新: %1").arg(value ? "保存" : "不保存"));
        }
        emit parametersChanged();
    });

    connect(saveJsonResultsCheckBox_, &QCheckBox::stateChanged, [this](int state) {
        if (yoloAlgorithm_) {
            bool value = state == Qt::Checked;
            yoloAlgorithm_->setSaveJsonResults(value);
            utils::Logger::instance().debug(QString("JSON检测结果保存设置已更新: %1").arg(value ? "保存" : "不保存"));
        }
        emit parametersChanged();
    });

    // 固定宽高设置连接
    connect(useFixedSizeCheckBox_, &QCheckBox::stateChanged, [this](int state) {
        bool value = state == Qt::Checked;
        fixedWidthSpinBox_->setEnabled(value);
        fixedHeightSpinBox_->setEnabled(value);
        if (yoloAlgorithm_) {
            yoloAlgorithm_->setUseFixedSize(value);
            if (value) {
                yoloAlgorithm_->setFixedSize(fixedWidthSpinBox_->value(), fixedHeightSpinBox_->value());
            }
        }
        emit parametersChanged();
    });

    connect(fixedWidthSpinBox_, QOverload<int>::of(&QSpinBox::valueChanged), [this](int value) {
        if (yoloAlgorithm_ && useFixedSizeCheckBox_->isChecked()) {
            yoloAlgorithm_->setFixedSize(value, fixedHeightSpinBox_->value());
        }
        emit parametersChanged();
    });

    connect(fixedHeightSpinBox_, QOverload<int>::of(&QSpinBox::valueChanged), [this](int value) {
        if (yoloAlgorithm_ && useFixedSizeCheckBox_->isChecked()) {
            yoloAlgorithm_->setFixedSize(fixedWidthSpinBox_->value(), value);
        }
        emit parametersChanged();
    });

    // 边界目标过滤设置连接
    connect(filterBoundaryTargetsCheckBox_, &QCheckBox::stateChanged, [this](int state) {
        if (yoloAlgorithm_) {
            bool value = state == Qt::Checked;
            yoloAlgorithm_->setFilterBoundaryTargets(value);
            boundaryThresholdSpinBox_->setEnabled(value);
            utils::Logger::instance().debug(QString("边界目标过滤设置已更新: %1").arg(value ? "启用" : "禁用"));
        }
        emit parametersChanged();
    });

    // 边界阈值设置连接
    connect(boundaryThresholdSpinBox_, QOverload<int>::of(&QSpinBox::valueChanged), [this](int value) {
        if (yoloAlgorithm_) {
            yoloAlgorithm_->setBoundaryThreshold(value);
            utils::Logger::instance().debug(QString("边界阈值已更新: %1 像素").arg(value));
        }
        emit parametersChanged();
    });
}

// 属性访问器

float RBCPanel::confidenceThreshold() const {
    return confidenceSpinBox_->value();
}

float RBCPanel::nmsThreshold() const {
    return nmsThresholdSpinBox_->value();
}

QString RBCPanel::modelPath() const {
    return modelPathEdit_->text();
}

void RBCPanel::setConfidenceThreshold(float value) {
    confidenceSpinBox_->setValue(value);
}

void RBCPanel::setNmsThreshold(float value) {
    nmsThresholdSpinBox_->setValue(value);
}

void RBCPanel::setModelPath(const QString& path) {
    modelPathEdit_->setText(path);
}

// 数据清理

void RBCPanel::clearResults() {
    // 清除算法中的预测结果
    yoloAlgorithm_->clearPredictions();

    // 如果当前有图像显示，刷新显示但不包含预测结果
    if (!currentImagePath_.isEmpty()) {
        cv::Mat img = utils::ImageIO::imageRead(currentImagePath_);
        if (!img.empty()) {
            // 只绘制标注数据（如果有）
            yoloAlgorithm_->drawGroundTruthOnImage(img);

            emit processedImageReady(img);
            utils::Logger::instance().debug("已清除预测结果");
        }
    }
}

void RBCPanel::clearAnnotations() {
    // 清除标注数据
    yoloAlgorithm_->clearGroundTruths();

    // 如果当前有图像显示，刷新显示但不包含标注
    if (!currentImagePath_.isEmpty()) {
        cv::Mat img = utils::ImageIO::imageRead(currentImagePath_);
        if (!img.empty()) {
            // 只显示原始图像，不绘制任何标注
            emit processedImageReady(img);
            utils::Logger::instance().debug("已清除标注数据");
        }
    }
}

void RBCPanel::setCurrentImage(const QString& imgPath) {
    currentImagePath_ = imgPath;

    yoloAlgorithm_->setCurrentImagePath(imgPath);

    // 如果加载过 GT，尝试在图像上叠加
    if (imgPath.isEmpty()) return;

    cv::Mat img = utils::ImageIO::imageRead(imgPath);
    if (img.empty()) {
        utils::Logger::instance().warning("无法加载图像: " + imgPath);
        return;
    }

    // 绘制预测结果（如果有）
    yoloAlgorithm_->drawPredictionsOnImage(img);  // 若无对应预测结果，会自动跳过

    // 绘制标注数据（如果有）
    yoloAlgorithm_->drawGroundTruthOnImage(img);   // 若无对应 GT，会自动跳过

    // 通过已有信号把结果送到 ImageView
    emit processedImageReady(img);
}

// 事件处理函数

void RBCPanel::onLoadModelClicked() {
    // 设置初始目录，优先使用models文件夹，然后是model文件夹
    QString initialDir = "models";
    QDir modelDir(initialDir);
    if (!modelDir.exists()) {
        initialDir = "model";
        modelDir.setPath(initialDir);
        if (!modelDir.exists()) {
            initialDir = QDir::currentPath(); // 使用当前工作目录
        }
    }

    QString modelPath = QFileDialog::getOpenFileName(this, "选择模型", initialDir, "模型文件 (*.onnx)");
    if (!modelPath.isEmpty()) {
        // 验证文件是否存在
        QFileInfo fileInfo(modelPath);
        if (!fileInfo.exists()) {
            utils::Logger::instance().warning("选择的模型文件不存在: " + modelPath);
            return;
        }

        // 验证文件是否可读
        if (!fileInfo.isReadable()) {
            utils::Logger::instance().warning("选择的模型文件不可读: " + modelPath);
            return;
        }

        modelPathEdit_->setText(modelPath);
        utils::Logger::instance().info("开始加载模型: " + modelPath);

        if (yoloAlgorithm_->loadModel(modelPath.toStdString())) {
            utils::Logger::instance().info("模型加载成功: " + modelPath);

            // 加载类别名称
            yoloAlgorithm_->loadClassNames(utils::ConfigManager::instance().getRBCClasses());
        } else {
            utils::Logger::instance().warning("模型加载失败: " + modelPath);
            // 清空模型路径显示
            modelPathEdit_->clear();
        }
    }
}

void RBCPanel::onPredictClicked() {
    if (currentImagePath_.isEmpty()) {
        utils::Logger::instance().info("请先加载图像");
        return;
    }

    // 检查模型是否已加载
    if (!yoloAlgorithm_->isModelLoaded()) {
        utils::Logger::instance().info("请先加载模型");
        return;
    }

    // 使用 QtConcurrent 运行预测
    QFuture<bool> future = QtConcurrent::run([this]() {
        cv::Mat input = utils::ImageIO::imageRead(currentImagePath_);
        if (input.empty()) {
            return false;
        }

        // 设置当前图像路径
        yoloAlgorithm_->setCurrentImagePath(currentImagePath_);

        // 调用predict函数
        return yoloAlgorithm_->predict(input);
    });

    predictWatcher_->setFuture(future);
    btnPredict_->setEnabled(false);
}

void RBCPanel::onPredictFutureFinished() {
    bool success = predictWatcher_->result();
    if (success) {
        // 加载原始图像用于绘制
        cv::Mat img = utils::ImageIO::imageRead(currentImagePath_);
        if (!img.empty()) {
            // 创建一个副本用于绘制
            cv::Mat output = img.clone();

            // 绘制预测结果
            yoloAlgorithm_->drawPredictionsOnImage(output);

            // 绘制标注数据（如果有）
            yoloAlgorithm_->drawGroundTruthOnImage(output);

            // 获取当前图像的检测结果
            QList<Detection> detections;
            if (yoloAlgorithm_->getPredictionsForImage(currentImagePath_, detections)) {
                // 发送检测结果信号
                emit detectionsReady(detections);
            }

            // 发送处理后的图像到UI显示
            emit processedImageReady(output);
            utils::Logger::instance().info("预测完成");
        } else {
            utils::Logger::instance().info("无法加载图像用于显示结果");
        }
    } else {
        utils::Logger::instance().info("预测失败，请检查图像和模型");
    }
    btnPredict_->setEnabled(true);
}

void RBCPanel::onPredictBatchClicked() {
    if (imagePaths_.isEmpty()) {
        utils::Logger::instance().info("请先加载图像");
        return;
    }

    // 检查模型是否已加载
    if (!yoloAlgorithm_->isModelLoaded()) {
        utils::Logger::instance().info("请先加载模型");
        return;
    }

    // 清除所有预测结果
    clearResults();

    // 启动批处理
    emit batchProcessStarted(imagePaths_.size());
    btnPredictBatch_->setEnabled(false);
    btnPredict_->setEnabled(false);
    btnLoadGT_->setEnabled(false);
    btnValidate_->setEnabled(false);

    // 设置进度回调函数
    yoloAlgorithm_->setProgressCallback([this](int current, int total) {
        QMetaObject::invokeMethod(this, "updateBatchProgress",
            Qt::QueuedConnection, Q_ARG(int, current), Q_ARG(int, total));
    });

    // 使用 QtConcurrent 运行批处理
    QFuture<bool> future = QtConcurrent::run([this]() {
        return yoloAlgorithm_->batchPredict(imagePaths_);
    });

    batchPredictWatcher_->setFuture(future);
}

void RBCPanel::onBatchPredictFutureFinished() {
    bool success = batchPredictWatcher_->result();
    if (success) {
        // 导出结果到CSV
        if (yoloAlgorithm_->exportBatchPredictions()) {
            utils::Logger::instance().info("批量预测完成，结果已导出");
        } else {
            utils::Logger::instance().info("批量预测完成，但结果导出失败");
        }

        // 收集所有检测结果
        QList<Detection> allDetections;
        cv::Mat lastImage;
        QString lastImagePath;

        // 遍历所有预测结果
        const auto& predictedResults = yoloAlgorithm_->getAllPredictions();
        for (auto it = predictedResults.begin(); it != predictedResults.end(); ++it) {
            const QString& imagePath = it.key();
            const QList<Detection>& detections = it.value();
            lastImagePath = imagePath;

            for (const auto& detection : detections) {
                Detection det = detection;
                det.imagePath = imagePath.toStdString();
                allDetections.append(det);
            }
        }

        // 加载最后一张图像用于显示
        if (!lastImagePath.isEmpty()) {
            lastImage = utils::ImageIO::imageRead(lastImagePath);
        }

        // 发送检测结果
        if (!allDetections.isEmpty()) {
            emit detectionsReady(allDetections);
        }

        // 计算并发送结果统计
        if (!allDetections.isEmpty()) {
            // 计算统计数据
            int totalImages = imagePaths_.size();
            int totalDetections = allDetections.size();
            double averagePerImage = totalImages > 0 ? static_cast<double>(totalDetections) / totalImages : 0.0;

            // 统计各标签的数量
            QMap<QString, int> labelCounts;
            for (const auto& detection : allDetections) {
                QString label = QString::fromStdString(detection.label);
                labelCounts[label]++;
            }

            // 发送结果统计信号
            emit batchStatisticsReady("RBC", totalImages, totalDetections, averagePerImage, labelCounts);
        }

        // 更新图像显示
        if (!currentImagePath_.isEmpty()) {
            yoloAlgorithm_->setCurrentImagePath(currentImagePath_);
            cv::Mat img = utils::ImageIO::imageRead(currentImagePath_);
            if (!img.empty()) {
                yoloAlgorithm_->drawPredictionsOnImage(img);
                yoloAlgorithm_->drawGroundTruthOnImage(img);
                emit processedImageReady(img);
            } else if (!lastImage.empty()) {
                emit processedImageReady(lastImage);
            }
        } else if (!lastImage.empty()) {
            emit processedImageReady(lastImage);
        }
    } else {
        utils::Logger::instance().info("批量预测失败，请检查图像和模型");
    }

    emit batchProcessFinished();
    btnPredictBatch_->setEnabled(true);
    btnPredict_->setEnabled(true);
    btnLoadGT_->setEnabled(true);
    btnValidate_->setEnabled(true);
}

void RBCPanel::onLoadGTClicked() {
    QString gtDir = QFileDialog::getExistingDirectory(this, "选择标注文件目录", "", QFileDialog::ShowDirsOnly);
    if (gtDir.isEmpty()) {
        return;
    }

    QDir dir(gtDir);
    QStringList filters;
    filters << "*.txt" << "*.json";
    dir.setNameFilters(filters);
    QStringList files = dir.entryList(QDir::Files);

    // 转换为完整路径
    QStringList gtPaths;
    for (const QString& file : files) {
        gtPaths << dir.absoluteFilePath(file);
    }

    if (gtPaths.isEmpty()) {
        utils::Logger::instance().info("所选目录中未找到标注文件");
        return;
    }

    if (yoloAlgorithm_->loadGroundTruthBatch(gtPaths)) {
        // 收集所有标注结果
        QList<Detection> allAnnotations;
        for (const QString& imagePath : imagePaths_) {
            QList<Detection> annotations = yoloAlgorithm_->getGroundTruthForImage(imagePath);
            for (const auto& annotation : annotations) {
                Detection anno = annotation;
                anno.imagePath = imagePath.toStdString();
                allAnnotations.append(anno);
            }
        }

        if (!allAnnotations.isEmpty()) {
            emit annotationsReady(allAnnotations);
        }

        // 更新显示
        if (!currentImagePath_.isEmpty()) {
            cv::Mat img = utils::ImageIO::imageRead(currentImagePath_);
            if (!img.empty()) {
                yoloAlgorithm_->drawPredictionsOnImage(img);
                yoloAlgorithm_->drawGroundTruthOnImage(img);
                emit processedImageReady(img);
            }
        }
    } else {
        utils::Logger::instance().info("标注数据加载失败");
    }
}

void RBCPanel::onValidateClicked() {
    if (!yoloAlgorithm_->isModelLoaded()) {
        utils::Logger::instance().info("请先加载模型");
        return;
    }

    if (yoloAlgorithm_->getAllPredictions().isEmpty()) {
        utils::Logger::instance().info("请先进行批量预测");
        return;
    }

    if (yoloAlgorithm_->getAllGroundTruth().isEmpty()) {
        utils::Logger::instance().info("请先加载标注");
        return;
    }

    btnValidate_->setEnabled(false);
    utils::Logger::instance().info("开始验证，请稍候...");

    QFuture<EvaluationMetrics> future = QtConcurrent::run([this]() {
        return yoloAlgorithm_->validatePredictions();
    });

    validateWatcher_->setFuture(future);
}

void RBCPanel::onValidateFutureFinished() {
    lastValidationMetrics_ = validateWatcher_->result();
    emit validationCompleted(lastValidationMetrics_);
    btnValidate_->setEnabled(true);
    utils::Logger::instance().info("验证完成");
}

void RBCPanel::onCropAnnotationsClicked() {
    // 检查是否已加载标注数据
    if (yoloAlgorithm_->getAllGroundTruth().isEmpty()) {
        utils::Logger::instance().info("请先加载标注数据");
        return;
    }

    // 检查是否有图像路径
    if (imagePaths_.isEmpty()) {
        utils::Logger::instance().info("请先加载图像");
        return;
    }

    btnCropAnnotations_->setEnabled(false);
    utils::Logger::instance().info("开始裁切标注，请稍候...");

    // 使用ConfigManager中配置的结果路径
    QString annotationDir = utils::ConfigManager::instance().getRBCResultsBaseDir() + "/annotation";
    QDir dir(annotationDir);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            utils::Logger::instance().warning(QString("无法创建annotation目录: %1").arg(annotationDir));
            btnCropAnnotations_->setEnabled(true);
            return;
        }
    }

    int totalProcessed = 0;
    int totalCropped = 0;

    // 遍历所有有标注的图像
    const auto& allGroundTruth = yoloAlgorithm_->getAllGroundTruth();
    for (auto it = allGroundTruth.begin(); it != allGroundTruth.end(); ++it) {
        const QString& imageBaseName = it.key();
        const QList<Detection>& annotations = it.value();

        // 查找对应的完整图像路径
        QString fullImagePath;
        for (const QString& imagePath : imagePaths_) {
            QFileInfo fileInfo(imagePath);
            if (fileInfo.completeBaseName() == imageBaseName) {
                fullImagePath = imagePath;
                break;
            }
        }

        if (fullImagePath.isEmpty()) {
            utils::Logger::instance().warning(QString("未找到图像文件: %1").arg(imageBaseName));
            continue;
        }

        // 加载图像
        cv::Mat image = utils::ImageIO::imageRead(fullImagePath);
        if (image.empty()) {
            utils::Logger::instance().warning(QString("无法加载图像: %1").arg(fullImagePath));
            continue;
        }

        totalProcessed++;

        // 按分类裁切标注
        for (const Detection& annotation : annotations) {
            // 创建类别目录
            QString categoryDir = annotationDir + "/" + QString::fromStdString(annotation.label);
            QDir catDir(categoryDir);
            if (!catDir.exists()) {
                if (!catDir.mkpath(".")) {
                    utils::Logger::instance().warning(QString("无法创建类别目录: %1").arg(categoryDir));
                    continue;
                }
            }

            // 转换坐标 - 标注数据存储为归一化值乘以1000
            int imgWidth = image.cols;
            int imgHeight = image.rows;
            cv::Rect scaled_bbox;
            scaled_bbox.x = static_cast<int>((annotation.bbox.x / 1000.0f) * imgWidth);
            scaled_bbox.y = static_cast<int>((annotation.bbox.y / 1000.0f) * imgHeight);
            scaled_bbox.width = static_cast<int>((annotation.bbox.width / 1000.0f) * imgWidth);
            scaled_bbox.height = static_cast<int>((annotation.bbox.height / 1000.0f) * imgHeight);

            // 计算裁切区域（在标注框基础上向外扩展10个像素）
            int expansion = 10;
            // ----- 修改后代码 -----
            // 使用 scaled_bbox 替代 annotation.bbox
            cv::Rect cropRect(
                std::max(0, scaled_bbox.x - expansion),
                std::max(0, scaled_bbox.y - expansion),
                std::min(scaled_bbox.width + 2 * expansion, image.cols - std::max(0, scaled_bbox.x - expansion)),
                std::min(scaled_bbox.height + 2 * expansion, image.rows - std::max(0, scaled_bbox.y - expansion))
            );
            // ----- 修改后代码 -----

            // 检查裁切区域是否有效
            if (cropRect.width <= 0 || cropRect.height <= 0) {
                // （您可以在这里保留或添加更详细的日志，例如打印 scaled_bbox 和 cropRect）
                utils::Logger::instance().warning(QString("忽略无效裁切区域: [scaled_bbox: x=%1, y=%2, w=%3, h=%4] [cropRect: w=%5, h=%6]")
                                                  .arg(scaled_bbox.x).arg(scaled_bbox.y).arg(scaled_bbox.width).arg(scaled_bbox.height)
                                                  .arg(cropRect.width).arg(cropRect.height));
                continue;
            }

            // 裁切图像
            cv::Mat cropped = image(cropRect).clone();

            // 生成输出文件名
            QFileInfo fileInfo(fullImagePath);
            QString outputPath = categoryDir + "/" + fileInfo.completeBaseName() + "_" +
                                 QString::number(totalCropped) + "_" +
                                 QString::fromStdString(annotation.label) + ".bmp";

            // 保存裁切图像
            bool saved = utils::ImageIO::imageWrite(outputPath, cropped);
            if (saved) {
                totalCropped++;
            } else {
                utils::Logger::instance().warning(QString("无法保存裁切图像: %1").arg(outputPath));
            }
        }
    }

    btnCropAnnotations_->setEnabled(true);
    utils::Logger::instance().info(QString("标注裁切完成，处理了%1张图像，共裁切%2个目标到%3文件夹")
                                   .arg(totalProcessed).arg(totalCropped).arg(annotationDir));
}

} // namespace RBC
} // namespace ui
