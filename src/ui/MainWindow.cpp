#include "ui/MainWindow.h"
#include "utils/Logger.h"
#include "utils/ImageIO.h"
#include "utils/ConfigManager.h"
#include "ui/WBC/WBCPanel.h"
#include <opencv2/imgcodecs.hpp>
#include <QStyleFactory>
#include <QStatusBar>
#include <QApplication>
#include <QFont>
#include <QFileDialog>
#include <QDir>
#include <QMessageBox>
#include <QVBoxLayout>
#include <QTimer>

MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent) {
    // 初始化配置管理器
    if (!utils::ConfigManager::instance().initialize()) {
        utils::Logger::instance().error("配置初始化失败");
        QMessageBox::critical(this, "错误", "配置文件加载失败，请检查config/default_config.json文件");
        QTimer::singleShot(0, this, &QMainWindow::close);
        return;
    }

    setupUI();
    createConnections();

    // 设置状态栏
    statusBar_ = new QStatusBar(this);
    setStatusBar(statusBar_);

    // 在状态栏右下角添加版本号信息
    QLabel* versionLabel = new QLabel("v1.1.0", this);
    versionLabel->setStyleSheet("QLabel { color: gray; font-size: 10px; margin-right: 10px; }");
    statusBar_->addPermanentWidget(versionLabel);

    // 设置初始算法类型（默认为WBC）
    imageView_->setCurrentAlgorithm("WBC");
}

MainWindow::~MainWindow() {
}

void MainWindow::setupUI() {
    // 设置窗口标题和大小
    setWindowTitle("AUR2");
    resize(1600, 960);

    // 设置全局样式
    qApp->setStyle(QStyleFactory::create("Fusion"));
    qApp->setFont(QFont("Microsoft YaHei", 9));

    // 创建中央部件
    QWidget* centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
    mainLayout->setSpacing(5);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    // 创建工具栏
    toolBar_ = new ToolBar(this);
    toolBar_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    mainLayout->addWidget(toolBar_);
    // 创建内容布局
    QHBoxLayout* contentLayout = new QHBoxLayout();
    contentLayout->setSpacing(5);

    // 创建左侧面板容器
    QWidget* leftPanel = new QWidget(this);
    leftPanel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
    QVBoxLayout* leftLayout = new QVBoxLayout(leftPanel);
    leftLayout->setSpacing(5);
    leftLayout->setContentsMargins(0, 0, 0, 0);
    // 创建文件列表
    fileList_ = new FileList(this);
    fileList_->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
    leftLayout->addWidget(fileList_);
    // 创建算法选择器
    algorithmSelector_ = new AlgorithmSelector(this, this);
    algorithmSelector_->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    leftLayout->addWidget(algorithmSelector_);
    // 创建右侧标签页容器
    rightTabWidget_ = new QTabWidget(this);
    rightTabWidget_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 创建图像视图标签页
    QWidget* imageTabWidget = new QWidget();
    QVBoxLayout* imageTabLayout = new QVBoxLayout(imageTabWidget);
    imageTabLayout->setSpacing(5);
    imageTabLayout->setContentsMargins(5, 5, 5, 5);

    // 创建图像视图
    imageView_ = new ImageView(imageTabWidget);
    imageView_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    imageView_->setMaximumHeight(700); // 限制图像面板最大高度
    imageTabLayout->addWidget(imageView_, 1);

    // 创建日志视图
    logView_ = new LogView(imageTabWidget);
    logView_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    logView_->setMaximumHeight(150); // 限制日志视图高度
    imageTabLayout->addWidget(logView_);

    // 添加图像视图标签页
    rightTabWidget_->addTab(imageTabWidget, "图像视图");

    // 创建结果统计标签页
    allChannelResultsPanel_ = new AllChannelResultsPanel(this);
    allChannelResultsPanel_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    rightTabWidget_->addTab(allChannelResultsPanel_, "结果统计");

    // 创建性能指标标签页
    performanceWidget_ = new ui::common::PerformanceResultsWidget(this);
    performanceWidget_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    rightTabWidget_->addTab(performanceWidget_, "性能指标");
    // 添加左右面板到内容布局
    contentLayout->addWidget(leftPanel, 1);
    contentLayout->addWidget(rightTabWidget_, 4);

    // 添加内容布局到主布局
    mainLayout->addLayout(contentLayout);
}

void MainWindow::createConnections() {
    // 基础导航功能 - 使用信号转发模式
    connect(toolBar_, &ToolBar::openImageClicked,
            this, &MainWindow::on_openImageClicked);
    connect(toolBar_, &ToolBar::openFolderClicked,
            this, &MainWindow::on_openFolderClicked);
    connect(toolBar_, &ToolBar::prevClicked,
            this, &MainWindow::on_prevClicked);
    connect(toolBar_, &ToolBar::nextClicked,
            this, &MainWindow::on_nextClicked);

    // 保存图像功能 - 使用信号转发模式
    connect(toolBar_, &ToolBar::saveImageClicked,
            this, &MainWindow::on_saveImageClicked);

    // 更新时间戳功能
    connect(toolBar_, &ToolBar::updateTimestampClicked,
            this, &MainWindow::on_updateTimestampClicked);



    // 文件列表信号槽连接
    connect(fileList_, &FileList::currentFileChanged,
            this, &MainWindow::on_currentFileChanged);

    // 算法选择器信号槽连接
    connect(algorithmSelector_, &AlgorithmSelector::algorithmChanged,
            [this](const QString& algorithm) {
                utils::Logger::instance().info("当前算法: " + algorithm);
                // 通知 ImageView 算法已更改
                imageView_->setCurrentAlgorithm(algorithm);
            });

    connect(algorithmSelector_, &AlgorithmSelector::processedImageReady,
            [this](const cv::Mat& image) {
                // 使用 lambda 函数适配参数
                imageView_->setImage(image);
            });

    // 验证相关信号-槽连接
    connect(algorithmSelector_, &AlgorithmSelector::validationCompleted,
            [this](const EvaluationMetrics& metrics) {
                // 在性能指标标签页中显示性能指标
                performanceWidget_->setMetrics(metrics);
                rightTabWidget_->setCurrentWidget(performanceWidget_);
            });

    // 检测结果相关信号-槽连接
    connect(algorithmSelector_, &AlgorithmSelector::detectionsReady,
            [this](const QList<Detection>& detections) {
                // 在 ImageView 中显示检测结果
                imageView_->setDetections(detections);
            });

    // 标注结果相关信号-槽连接
    connect(algorithmSelector_, &AlgorithmSelector::annotationsReady,
            [this](const QList<Detection>& annotations) {
                // 在 ImageView 中显示标注结果
                imageView_->setAnnotations(annotations);
            });

    // 当文件或文件列表变化时，更新算法选择器
    connect(this, &MainWindow::currentImageChanged,
            algorithmSelector_, &AlgorithmSelector::setCurrentImage);
    connect(this, &MainWindow::imagePathsChanged,
            algorithmSelector_, &AlgorithmSelector::setImagePaths);

    connect(algorithmSelector_, &AlgorithmSelector::batchProcessStarted,
            this, &MainWindow::onBatchProcessStarted);
    connect(algorithmSelector_, &AlgorithmSelector::batchProcessProgress,
            this, &MainWindow::onBatchProcessProgress);
    connect(algorithmSelector_, &AlgorithmSelector::batchProcessFinished,
            this, &MainWindow::onBatchProcessFinished);

    // 批量预测结果统计信号连接
    connect(algorithmSelector_, &AlgorithmSelector::batchStatisticsReady,
            [this](const QString& algorithm, int totalImages, int totalDetections,
                   double averagePerImage, const QMap<QString, int>& labelCounts) {
                // 更新所有通道结果统计面板
                allChannelResultsPanel_->updateAlgorithmResults(algorithm, totalImages,
                                                               totalDetections, averagePerImage, labelCounts);
            });
}

void MainWindow::showCurrentImage() {
    if (currentImageIndex_ >= 0 && currentImageIndex_ < fullPathList_.size()) {
        imagePath_ = fullPathList_[currentImageIndex_];

        // 使用 ImageIO 读取图像
        cv::Mat img = utils::ImageIO::imageRead(imagePath_);

        if (!img.empty()) {
            // 传递图像和路径
            imageView_->setImage(img, imagePath_);
        } else {
            utils::Logger::instance().warning(QString("无法加载图像: %1").arg(imagePath_));
        }
    }
}

// 槽函数实现
void MainWindow::on_openImageClicked() {
    QString file = QFileDialog::getOpenFileName(this, "选择图像", "",
        "Images (*.png *.jpg *.jpeg *.bmp *.tiff *.tif *.raw *.bin *.data *.yuv *.rgb *.bgr *.gray *.grey)");
    if (!file.isEmpty()) {
        imagePath_ = file;
        fullPathList_.clear();
        fullPathList_ << file;
        currentImageIndex_ = 0;
        fileList_->clear();
        fileList_->addFiles(fullPathList_);
        fileList_->setCurrentIndex(0);
        showCurrentImage();
        utils::Logger::instance().info("已加载图像: " + file);

        emit currentImageChanged(file);
        emit imagePathsChanged(fullPathList_);
    }
}

void MainWindow::on_openFolderClicked() {
    QString folder = QFileDialog::getExistingDirectory(this, "选择图像文件夹");
    if (!folder.isEmpty()) {
        QDir dir(folder);
        QStringList filters = {"*.png", "*.jpg", "*.jpeg", "*.bmp", "*.tiff", "*.tif",
                              "*.raw", "*.bin", "*.data", "*.yuv", "*.rgb", "*.bgr", "*.gray", "*.grey"};
        QStringList imageList = dir.entryList(filters, QDir::Files);

        fileList_->clear();
        fullPathList_.clear();

        for (const QString &img : imageList) {
            QString fullPath = dir.absoluteFilePath(img);
            fullPathList_ << fullPath;
        }

        fileList_->addFiles(fullPathList_);

        if (!fullPathList_.isEmpty()) {
            currentImageIndex_ = 0;
            fileList_->setCurrentIndex(0);
            showCurrentImage();
            utils::Logger::instance().info(QString("已加载文件夹: %1 (共%2张图像)").arg(folder).arg(fullPathList_.size()));

            emit currentImageChanged(fullPathList_[0]);
            emit imagePathsChanged(fullPathList_);
        }
    }
}

void MainWindow::on_prevClicked() {
    if (currentImageIndex_ > 0) {
        currentImageIndex_--;
        fileList_->setCurrentIndex(currentImageIndex_);
        showCurrentImage();

        emit currentImageChanged(fullPathList_[currentImageIndex_]);
    }
}

void MainWindow::on_nextClicked() {
    if (currentImageIndex_ < fullPathList_.size() - 1) {
        currentImageIndex_++;
        fileList_->setCurrentIndex(currentImageIndex_);
        showCurrentImage();

        emit currentImageChanged(fullPathList_[currentImageIndex_]);
    }
}

void MainWindow::on_currentFileChanged(const QString& file) {
    int index = fullPathList_.indexOf(file);
    if (index != -1) {
        currentImageIndex_ = index;
        imagePath_ = file;
        showCurrentImage();

        emit currentImageChanged(file);
    }
}

void MainWindow::onBatchProcessStarted(int total) {
    statusBar_->showMessage("批处理中...");

    // 屏蔽文件列表，防止用户在批量预测时切换图像
    fileList_->setEnabled(false);
}

void MainWindow::onBatchProcessProgress(int current, int total) {
    statusBar_->showMessage(QString("批处理中... %1/%2").arg(current).arg(total));
}

void MainWindow::onBatchProcessFinished() {
    statusBar_->showMessage("批处理完成", 3000);

    // 重新启用文件列表
    fileList_->setEnabled(true);
}

void MainWindow::on_saveImageClicked() {
    // 检查是否有图像
    if (imagePath_.isEmpty()) {
        utils::Logger::instance().warning("没有图像可保存");
        return;
    }

    // 获取保存路径
    QString saveFilePath = QFileDialog::getSaveFileName(
        this,
        "保存图像",
        QFileInfo(imagePath_).fileName(),
        "图像文件 (*.png *.jpg *.jpeg *.bmp *.tiff *.tif)"
    );

    if (saveFilePath.isEmpty()) {
        return;
    }

    // 获取当前显示的图像（包含检测/标注结果）
    cv::Mat displayImage = imageView_->getCurrentDisplayImage();

    if (displayImage.empty()) {
        utils::Logger::instance().warning("无法获取当前显示图像");
        return;
    }

    // 保存图像
    if (utils::ImageIO::imageWrite(saveFilePath, displayImage)) {
        utils::Logger::instance().info("图像已保存到: " + saveFilePath);
        statusBar_->showMessage("图像已保存", 3000);
    } else {
        utils::Logger::instance().info("保存图像失败: " + saveFilePath);
        statusBar_->showMessage("保存图像失败", 3000);
    }
}

void MainWindow::on_updateTimestampClicked() {
    // 更新ConfigManager的时间戳
    utils::ConfigManager::instance().updateTimestamp();

    // 清零所有通道的结果统计（相当于加载新样本）
    allChannelResultsPanel_->clearAllResults();

    utils::Logger::instance().info("时间戳已更新，所有通道结果统计已清零");
}