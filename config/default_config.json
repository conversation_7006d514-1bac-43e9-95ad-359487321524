{"wbc_model": {"default_model_path": "models/WBC.onnx", "classes": ["UWBC", "CWBC", "LYM", "MON", "EOS", "BAS", "BAND", "SEG", "NSG", "RET"]}, "rbc_model": {"default_model_path": "models/RBC.onnx", "classes": ["RBC", "PLT"]}, "plt_model": {"default_model_path": "models/PLT.onnx", "classes": ["PLT", "APLT", "UNR"]}, "hgb_config": {"default_calibration_coef": {"hgb": 1.0, "mcv": 1.0, "plt": 1.0, "rbc": 1.0, "wbc": 1.0}, "channel_selection": {"select_baso": true, "select_diff": true, "select_nrbc": true, "select_ret": true, "select_cbc": true}, "data_validation": {"enable_crc_check": true, "max_file_size_mb": 100, "min_measurement_points": 1, "max_measurement_points": 100000}}, "detection": {"confidence_threshold": 0.25, "nms_threshold": 0.5, "save_results": false, "save_txt_results": false, "use_fixed_size": false, "fixed_width": 100, "fixed_height": 100, "max_display_per_category": 500, "filter_boundary_targets": false, "boundary_threshold": 20}, "paths": {"results": {"base_dir": "results/WBC", "crop_dir": "results/WBC/results_crop", "txt_dir": "results/WBC/results_txt", "json_dir": "results/WBC/results_json", "csv_dir": "results/WBC/results_csv"}, "hgb_results": {"base_dir": "results/HGB", "csv_dir": "results/HGB/results_csv", "log_dir": "results/HGB/logs"}}, "annotation": {"default_image_width": 640, "default_image_height": 480}, "validation": {"iou_threshold": 0.5}}